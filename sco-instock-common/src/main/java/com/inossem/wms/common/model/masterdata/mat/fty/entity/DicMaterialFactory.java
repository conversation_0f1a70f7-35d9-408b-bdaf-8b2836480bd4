package com.inossem.wms.common.model.masterdata.mat.fty.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 物料工厂数据表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicMaterialFactory对象", description = "物料工厂数据表")
@TableName("dic_material_factory")
public class DicMaterialFactory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "验收分类id" , example = "149931421663233")
    private Long inspectClassifyId;

    @ApiModelProperty(value = "物料分类id" , example = "1")
    private Long materialClassifyId;

    @ApiModelProperty(value = "标签类型 0：普通标签 1：RFID抗金属  2：RFID非抗金属" , example = "0")
    private Integer tagType;

    @ApiModelProperty(value = "单品/批次  0批次 1单品" , example = "1")
    private Integer isSingle;

    @ApiModelProperty(value = "移动平均价" , example = "100")
    private BigDecimal moveAvgPrice;

    @ApiModelProperty(value = "价格单位" , example = "M3")
    private String priceUnit;

    @ApiModelProperty(value = "计价方式: V-移动平均 S-标准价" , example = "V")
    private String priceMethod;

    @ApiModelProperty(value = "标准价格" , example = "100")
    private BigDecimal standardPrice;

    @ApiModelProperty(value = "保质期" , example = "100")
    private Integer shelfLife;

    @ApiModelProperty(value = "临期预警天数" , example = "10")
    private Integer remindDay;

    @ApiModelProperty(value = "安全库存数量" , example = "1000")
    private BigDecimal securityQty;

    @ApiModelProperty(value = "订货点数量" , example = "1000")
    private BigDecimal orderPointQty;

    @ApiModelProperty(value = "是否启用ERP批次【1是，0否】" , example = "1")
    private Integer isBatchErpEnabled;

    @ApiModelProperty(value = "是否启用生产批次【1是，0否】" , example = "1")
    private Integer isBatchProductEnabled;

    @ApiModelProperty(value = "是否启用包装物【1是，0否】" , example = "1")
    private Integer isPackageEnabled;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "总货架寿命")
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "最小货架寿命")
    private Integer shelfLifeMin;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;
    private Integer unitizedFlag;
    // 库存确定组
    private String stockGroup;
    private Long stockGroupId;
    // 专业维保标识
    private Integer maintainProFlag;

    @ApiModelProperty(value = "采购组id")
    private Long purchaseGroupId;

    @ApiModelProperty(value = "采购组编码")
    private String purchaseGroupCode;
}