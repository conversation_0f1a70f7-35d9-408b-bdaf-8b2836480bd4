package com.inossem.wms.common.model.masterdata.mat.fty.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料工厂数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "物料工厂数据传输对象", description = "物料工厂数据传输对象")
public class DicMaterialFactoryImport implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/

    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码" , example = "8000")
    @ExcelProperty(value = "工厂编码", index =0)
    private String ftyCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @ExcelProperty(value = "物料编码", index =1)
    private String matCode;

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    @ExcelIgnore
    private String createUserCode;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    @ExcelIgnore
    private String modifyUserCode;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private Long id;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id" , example = "60000001")
    @ExcelIgnore
    private Long matId;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    @ExcelIgnore
    private Long ftyId;

    @RlatAttr(rlatTableName = "biz_spec_classify", sourceAttrName = "specClassifyCode,specClassifyName", targetAttrName = "inspectClassifyCode,inspectClassifyName")
    @ApiModelProperty(value = "验收分类id" , example = "149931421663233")
    @ExcelProperty(value = "验收分类id", index =2)
    private Long inspectClassifyId;

    @RlatAttr(rlatTableName = "biz_spec_classify", sourceAttrName = "specClassifyCode,specClassifyName", targetAttrName = "materialClassifyCode,materialClassifyName")
    @ApiModelProperty(value = "物料分类id" , example = "1")
    @ExcelProperty(value = "物料分类id", index =3)
    private Long materialClassifyId;

    @ApiModelProperty(value = "标签类型 0：普通标签 1：RFID抗金属  2：RFID非抗金属" , example = "0")
    @ExcelProperty(value = "标签类型 0：普通标签 1：RFID抗金属  2：RFID非抗金属", index =4)
    private Integer tagType;

    @ApiModelProperty(value = "单品/批次  0批次 1单品" , example = "1")
    @ExcelProperty(value = "单品/批次", index =5)
    private Integer isSingle;

    @ApiModelProperty(value = "移动平均价" , example = "100")
    @ExcelProperty(value = "移动平均价", index =6)
    private BigDecimal moveAvgPrice;

    @ApiModelProperty(value = "价格单位" , example = "M3")
    @ExcelProperty(value = "价格单位", index =7)
    private String priceUnit;

    @ApiModelProperty(value = "计价方式: V-移动平均 S-标准价" , example = "V")
    @ExcelProperty(value = "计价方式: V-移动平均 S-标准价", index =8)
    private String priceMethod;

    @ApiModelProperty(value = "标准价格" , example = "100")
    @ExcelProperty(value = "标准价格", index = 9)
    private BigDecimal standardPrice;

    @ApiModelProperty(value = "总货架寿命")
    @ExcelProperty(value = "总货架寿命", index = 10)
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "最小货架寿命")
    @ExcelProperty(value = "最小货架寿命", index = 11)
    private Integer shelfLifeMin;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    @ExcelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）", index = 12)
    private Integer packageType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    @ExcelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）", index = 13)
    private Integer depositType;

    @ApiModelProperty(value = "保质期" , example = "100")
    @ExcelProperty(value = "保质期", index = 14)
    private Integer shelfLife;

    @ApiModelProperty(value = "临期预警天数" , example = "10")
    @ExcelProperty(value = "临期预警天数", index = 15)
    private Integer remindDay;

    @ApiModelProperty(value = "安全库存数量" , example = "1000")
    @ExcelProperty(value = "安全库存数量", index = 16)
    private BigDecimal securityQty;

    @ApiModelProperty(value = "订货点数量" , example = "1000")
    @ExcelProperty(value = "订货点数量", index = 17)
    private BigDecimal orderPointQty;

    @ApiModelProperty(value = "是否启用ERP批次【1是，0否】" , example = "1")
    @ExcelProperty(value = "是否启用ERP批次【1是，0否】", index = 18)
    private Integer isBatchErpEnabled;

    @ApiModelProperty(value = "是否启用生产批次【1是，0否】" , example = "1")
    @ExcelProperty(value = "是否启用生产批次【1是，0否】", index = 19)
    private Integer isBatchProductEnabled;

    @ApiModelProperty(value = "是否启用包装物【1是，0否】" , example = "1")
    @ExcelProperty(value = "是否启用包装物【1是，0否】", index = 20)
    private Integer isPackageEnabled;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ExcelIgnore
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ExcelIgnore
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
    @ExcelIgnore
    private Integer unitizedFlag;
    // 库存确定组
    @ExcelIgnore
    private String stockGroup;
    @ExcelIgnore
    private Long stockGroupId;
    // 专业维保标识
    @ExcelIgnore
    private Integer maintainProFlag;
}
