package com.inossem.wms.common.model.masterdata.mat.fty.po;

import java.io.Serializable;

import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "物料工厂保存入参类", description = "物料工厂保存入参类")
@Data
public class DicMaterialFactorySavePO implements Serializable {

    private static final long serialVersionUID = 8413511905304847422L;

    @ApiModelProperty(value = "物料工厂实体类")
    private DicMaterialFactoryDTO materialFactoryInfo;
}
