package com.inossem.wms.common.model.masterdata.facility.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 设施管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicFacility对象", description = "设施管理表")
@TableName("dic_facility")
public class DicFacility implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("设施编码")
    private String facilityCode;

    @ApiModelProperty("设施描述")
    private String facilityName;

    @ApiModelProperty("设施位置描述")
    private String locationDes;

    @ApiModelProperty("设施费用（USD/天）")
    private BigDecimal cost;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("预订日期")
    private Date targetDate;

    @TableLogic
    @ApiModelProperty("是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @ApiModelProperty("创建人id")
    private Long createUserId;

    @ApiModelProperty("修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "合同id(biz_receipt_contract_head表id)")
    private Long contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

}
