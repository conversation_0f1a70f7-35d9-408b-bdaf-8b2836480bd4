package com.inossem.wms.common.model.masterdata.receipt.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/5/2 11:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "单据类型对应存储区传输对象", description = "单据类型对应存储区传输对象")
public class BizReceiptTypeDTO  implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据类型描述")
    private String receiptTypeName;

    @ApiModelProperty(value = "仓库号id")
    private Long whId;

    @ApiModelProperty(value = "接收仓库存储类型id" , example = "152218403667969")
    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "typeCode,typeName")
    private Long typeId;

    @ApiModelProperty(value = "存储类型code" , example = "s008")
    private String typeCode;

    @ApiModelProperty(value = "存储类型name" , example = "s008")
    private String typeName;

    @ApiModelProperty(value = "存储区id", example = "1")
    @RlatAttr(rlatTableName = "dic_wh_storage_section", sourceAttrName = "sectionCode,sectionName", targetAttrName = "sectionCode,sectionName")
    private Long sectionId;

    @ApiModelProperty(value = "存储区code" , example = "s008")
    private String sectionCode;

    @ApiModelProperty(value = "存储区name" , example = "s008")
    private String sectionName;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

}
