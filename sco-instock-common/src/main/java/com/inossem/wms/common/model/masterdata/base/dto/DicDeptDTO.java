package com.inossem.wms.common.model.masterdata.base.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/15 14:39
 */
@Data
@ApiModel(description = "部门")
public class DicDeptDTO implements Serializable {

    private static final long serialVersionUID = -2781690497085037733L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "部门id", example = "1100", required = true)
    private String deptCode;

    @ApiModelProperty(value = "公司id" , example = "1", required = false)
    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode,corpName", targetAttrName = "corpCode,corpName")
    private Long corpId;

    @ApiModelProperty(value = "填充属性 - 公司编码" , example = "1000")
    private String corpCode;

    @ApiModelProperty(value = "填充属性 - 公司描述" , example = "英诺森")
    private String corpName;

    @ApiModelProperty(value = "部门描述" , example = "示例部门", required = false)
    private String deptName;

    @ApiModelProperty(value = "部门类别" , example = "1", required = false)
    private String deptType;

    @ApiModelProperty(value = "用户审批级别" , example = "1", required = false)
    private Integer jobLevel;

    @ApiModelProperty(value = "部门类别-国际化")
    private String deptTypeI18n;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String createUserName;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "科室" , example = "科室集合")
    private List<DicDeptOfficeDTO> officeDTOList;

    @ApiModelProperty(value = "部门是否选中" , example = "1")
    private String deptIsChecked;

}
