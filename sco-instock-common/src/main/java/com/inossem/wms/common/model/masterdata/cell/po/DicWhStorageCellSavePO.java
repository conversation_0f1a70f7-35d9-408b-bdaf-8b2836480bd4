package com.inossem.wms.common.model.masterdata.cell.po;

import com.inossem.wms.common.model.masterdata.cell.dto.DicWhStorageCellDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 存储单元主数据保存入参对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "存储单元主数据保存入参对象", description = "存储单元主数据保存入参对象")
public class DicWhStorageCellSavePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "存储单元信息入参")
    private DicWhStorageCellDTO dicWhStorageCellInfo;

}
