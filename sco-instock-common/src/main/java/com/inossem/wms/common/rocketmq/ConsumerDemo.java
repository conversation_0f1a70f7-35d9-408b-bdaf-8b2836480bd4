package com.inossem.wms.common.rocketmq;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.common.annotation.WmsMQListener;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> MQ消费者demo
 */
@Component
@Slf4j
public class ConsumerDemo {

    @WmsMQListener(tags = "tag3", rollback = "callBack")
    public void consumerHandler(Object obj) {
        log.info("消费者【接收】msg:{}", JSON.toJSONString(obj));
        // TODO 业务
        System.out.println(1 / 0);
        String handl = JSON.toJSONString(obj).concat("==已处理");
        log.info("消费者【处理】msg:{}", JSON.toJSONString(handl));
    }

    private void callBack(Object obj) {
        System.out.println("====业务回调====" + JSONObject.toJSONString(obj));
    }
}
