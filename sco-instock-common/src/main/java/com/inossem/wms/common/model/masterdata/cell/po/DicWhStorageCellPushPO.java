package com.inossem.wms.common.model.masterdata.cell.po;

import com.inossem.wms.common.model.masterdata.cell.dto.DicWhStorageCellDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 存储单元类推送设备状态、电量po
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-012-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "存储单元推送设备状态、电量po", description = "存储单元推送设备状态、电量po")
public class DicWhStorageCellPushPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "存储单元推送设备集合")
    private List<DicWhStorageCellDTO> electronicScaleList;

}
