package com.inossem.wms.common.model.masterdata.mat.fty.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料工厂分页对象出参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "物料工厂分页对象出参", description = "物料工厂分页对象出参")
public class DicMaterialFactoryPageVO {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/

    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private String ftyCode;

    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称" , example = "物料描述001003")
    private String matName;

    /**
     * 物料类型编码
     */
    @ApiModelProperty(value = "物料类型编码" , example = "A1")
    private String matTypeCode;

    /**
     * 物料类型名称
     */
    @ApiModelProperty(value = "物料类型名称" , example = "物料类型1")
    private String matTypeName;

    /**
     * 物料组编码
     */
    @ApiModelProperty(value = "物料组编码" , example = "g1")
    private String matGroupCode;

    /**
     * 物料组名称
     */
    @ApiModelProperty(value = "物料组名称" , example = "物料组1")
    private String matGroupName;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码" , example = "7")
    private String unitCode;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称" , example = "立方米")
    private String unitName;

    /**
     * 价格单位编码
     */
    @ApiModelProperty(value = "价格单位编码" , example = "M3")
    private String priceUnitCode;

    /**
     * 价格单位描述
     */
    @ApiModelProperty(value = "价格单位描述" , example = "立方米")
    private String priceUnitName;

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /**
     * 验收分类编码
     */
    @ApiModelProperty(value = "验收分类编码" , example = "F003")
    private String inspectClassifyCode;

    /**
     * 验收分类描述
     */
    @ApiModelProperty(value = "验收分类描述" , example = "测试")
    private String inspectClassifyName;

    /**
     * 物料分类编码
     */
    @ApiModelProperty(value = "物料分类编码" , example = "materialClassifyCode")
    private String materialClassifyCode;

    /**
     * 物料分类描述
     */
    @ApiModelProperty(value = "物料分类描述" , example = "物料分类描述")
    private String materialClassifyName;

    /**
     * 标签类型
     */
    @ApiModelProperty(value = "标签类型" , example = "1")
    private String tagTypeI18n;

    /**
     * 是否为单品
     */
    @ApiModelProperty(value = "是否为单品" , example = "1")
    private String isSingleI18n;

    /**
     * 是否启用ERP批次
     */
    @ApiModelProperty(value = "是否启用ERP批次" , example = "1")
    private String isBatchErpEnabledI18n;

    /**
     * 是否启用生产批次
     */
    @ApiModelProperty(value = "是否启用生产批次" , example = "1")
    private String isBatchProductEnabledI18n;

    /**
     * 是否启用包装物
     */
    @ApiModelProperty(value = "是否启用包装物" , example = "1")
    private String isPackageEnabledI18n;

    /**
     * 计价方式: V-移动平均 S-标准价
     */
    @ApiModelProperty(value = "计价方式: V-移动平均 S-标准价" , example = "V")
    private String priceMethodI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @RlatAttr(rlatTableName = "biz_spec_classify", sourceAttrName = "specClassifyCode,specClassifyName", targetAttrName = "inspectClassifyCode,inspectClassifyName")
    @ApiModelProperty(value = "验收分类id" , example = "149931421663233")
    private Long inspectClassifyId;

    @RlatAttr(rlatTableName = "biz_spec_classify", sourceAttrName = "specClassifyCode,specClassifyName", targetAttrName = "materialClassifyCode,materialClassifyName")
    @ApiModelProperty(value = "物料分类id" , example = "1")
    private Long materialClassifyId;

    @ApiModelProperty(value = "标签类型 0：普通标签 1：RFID抗金属  2：RFID非抗金属" , example = "0")
    private Integer tagType;

    @ApiModelProperty(value = "单品/批次  0批次 1单品" , example = "1")
    private Integer isSingle;

    @ApiModelProperty(value = "移动平均价" , example = "100")
    private BigDecimal moveAvgPrice;

    @ApiModelProperty(value = "价格单位" , example = "M3")
    private String priceUnit;

    @ApiModelProperty(value = "计价方式: V-移动平均 S-标准价" , example = "V")
    private String priceMethod;

    @ApiModelProperty(value = "标准价格" , example = "100")
    private BigDecimal standardPrice;

    @ApiModelProperty(value = "总货架寿命")
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "最小货架寿命")
    private Integer shelfLifeMin;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private String packageTypeI18n;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private String depositTypeI18n;

    @ApiModelProperty(value = "保质期" , example = "100")
    private Integer shelfLife;

    @ApiModelProperty(value = "临期预警天数" , example = "10")
    private Integer remindDay;

    @ApiModelProperty(value = "安全库存数量" , example = "1000")
    private BigDecimal securityQty;

    @ApiModelProperty(value = "订货点数量" , example = "1000")
    private BigDecimal orderPointQty;

    @ApiModelProperty(value = "是否启用ERP批次【1是，0否】" , example = "1")
    private Integer isBatchErpEnabled;

    @ApiModelProperty(value = "是否启用生产批次【1是，0否】" , example = "1")
    private Integer isBatchProductEnabled;

    @ApiModelProperty(value = "是否启用包装物【1是，0否】" , example = "1")
    private Integer isPackageEnabled;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
    private Integer unitizedFlag;
    private String unitizedFlagI18n;
    private String stockGroup;
}
