package com.inossem.wms.common.model.masterdata.spec.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 特性传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "特性传输对象", description = "特性传输对象")
public class BizSpecFeatureDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/
    /**
     * 创建人描述
     */
    @ApiModelProperty(value = "创建人描述", example = "管理员")
    private String createUserName;
    /**
     * 数据类型描述
     */
    @ApiModelProperty(value = "数据类型描述" , example = "文本框")
    private String specFeatureTypeI18n;
    /**
     * 特性值列表
     */
    @ApiModelProperty(value = "特性值列表")
    private List<BizSpecFeatureValueDTO> bizSpecFeatureValueDTOList;
    /**
     * 特性值id
     */
    @ApiModelProperty(value = "特性值id" , example = "145874414010369")
    private Long specValueId;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "特性ID" , example = "145874414010369")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "特性代码" , example = "biz_batch_info.batch_erp,biz_batch_info.spec_stock_code")
    private String specFeatureCode;

    @ApiModelProperty(value = "特性描述" , example = "特性描述1")
    private String specFeatureName;

    @ApiModelProperty(value = "数据类型 1文本框 2日期 3下拉选框 4单选框" , example = "1")
    private Integer specFeatureType;

    @ApiModelProperty(value = "特性是否必填0非必填1必填" , example = "1")
    private Integer required;

    @ApiModelProperty(value = "type为3下拉选框时使用，内部数据暂定为json" , example = "jsonText")
    private String info;

    @ApiModelProperty(value = "默认值" , example = "0")
    private String defaultValue;

    @ApiModelProperty(value = "显示顺序" , example = "1")
    private Integer displayIndex;

    @ApiModelProperty(value = "删除标识" , example = "0")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    @ApiModelProperty(value = "创建人" , example = "1")
    private Long createUserId;

    @ApiModelProperty(value = "修改人" , example = "1")
    private Long modifyUserId;

}
