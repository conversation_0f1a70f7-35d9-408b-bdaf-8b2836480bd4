package com.inossem.wms.common.model.masterdata.spec.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 特性表查询入参类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "特性表入查询参类", description = "特性表查询入参类")
public class BizSpecFeatureSearchPO extends PageCommon {

    private static final long serialVersionUID = -6661152208172473789L;

    @ApiModelProperty(value = "特性代码" , example = "biz_batch_info.batch_erp,biz_batch_info.spec_stock_code")
    private String specFeatureCode;

    @ApiModelProperty(value = "特性描述" , example = "特性描述1")
    private String specFeatureName;
}
