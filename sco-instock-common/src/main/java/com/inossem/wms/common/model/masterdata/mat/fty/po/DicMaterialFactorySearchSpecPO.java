package com.inossem.wms.common.model.masterdata.mat.fty.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 物料工厂查询特性入参类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "物料工厂查询特性入参类", description = "物料工厂查询特性入参类")
public class DicMaterialFactorySearchSpecPO {

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    @ApiModelProperty(value = "分类类型 1验收 2物料" , example = "1")
    private Integer specClassifyType;
}
