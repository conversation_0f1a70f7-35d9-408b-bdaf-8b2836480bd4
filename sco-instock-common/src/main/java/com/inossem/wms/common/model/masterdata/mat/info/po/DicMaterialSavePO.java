package com.inossem.wms.common.model.masterdata.mat.info.po;

import java.io.Serializable;

import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "物料保存入参类", description = "物料保存入参类")
@Data
public class DicMaterialSavePO implements Serializable {

    private static final long serialVersionUID = -1982739142087195315L;

    @ApiModelProperty(value = "物料实体类")
    private DicMaterialDTO materialInfo;
}
