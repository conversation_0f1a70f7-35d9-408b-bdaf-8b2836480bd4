package com.inossem.wms.common.model.masterdata.car.po;

import com.inossem.wms.common.model.masterdata.car.dto.DicSlingDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 吊带管理保存/修改入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="吊带管理保存/修改入参", description="吊带管理保存/修改入参")
public class DicSlingSavePO implements Serializable {

    private static final long serialVersionUID = 7498900832761288139L;

    @ApiModelProperty(value = "吊带信息传输对象")
    private DicSlingDTO slingDTO;

}
