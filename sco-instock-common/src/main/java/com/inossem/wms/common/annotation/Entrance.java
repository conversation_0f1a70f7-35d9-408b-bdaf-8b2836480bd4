package com.inossem.wms.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 入口函数注解
 * <AUTHOR>
 * @date 2021/3/1 14:13
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Entrance {
	/**
	 * 可被编排的调用语句
	 * @return
	 */
	String[] call();
}
