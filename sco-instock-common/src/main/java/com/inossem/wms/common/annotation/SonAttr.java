package com.inossem.wms.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2021/2/1 9:37
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface SonAttr {

    /**
     * 子表表名
     * 
     * @return
     */
    String sonTbName();

    /**
     * 子表外键属性名
     * 
     * @return
     */
    String sonTbFkAttrName();

    /**
     * 主表主键属性名
     *
     * @return
     */
    String mainTbPkAttrName() default "id";

    /**
     * 主表某个属性是什么确定值的时候查询子表  receiptType=105,125
     * @return
     */
    String limitAttr() default "";
    /**
     * 子表其他属性条件限制
     *
     * @return
     */
    String[] sonTbFkOtherAttrLimit() default "";
}
