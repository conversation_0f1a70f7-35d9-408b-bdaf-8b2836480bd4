package com.inossem.wms.common.constant.task;

/**
 * 作业模块 常量
 * 
 * <AUTHOR>
 */
public class TaskConst {

    /**
     * 单据类型对应表判断 - 入库表,单据类型
     */
    public static final String RECEIPT_TYPE_INPUT = "biz_receipt_input_head:preReceiptType=211,212,213,214,215,216,219,815,106,8151,332,9104";
    /**
     * 单据类型对应表判断 - 出库表,单据类型
     */
    public static final String RECEIPT_TYPE_OUTPUT =
        "biz_receipt_output_head:preReceiptType=411,-411,4110,4111,4112,512,413,-413,4130,4131,414,-414,4140,4141,4142,415,-415,4150,416,418,-416,4160,4161,4162,4163,417,422,817,108,323,8171,333,9102,1323";

    /**
     * 单据类型对应表判断 - 转储表,单据类型
     */
    public static final String RECEIPT_TYPE_TRANSPORT = "biz_receipt_transport_head:preReceiptType=510,511,512,513,514,518,519";

    /**
     * 单据类型对应表判断 - 退库表,单据类型
     */
    public static final String RECEIPT_TYPE_RETURN = "biz_receipt_return_head:preReceiptType=311,312,313,321,1321,139";


    /**
     * 单据类型对应表判断 - 物流清关,单据类型
     */
    public static final String RECEIPT_TYPE_LOGISTICS =
            "biz_receipt_logistics_head:preReceiptType=223";


    public static final String RECEIPT_TYPE_DEMAND_PLAN =
            "biz_receipt_demand_plan_head:preReceiptType=400";

    public static final String RECEIPT_TYPE_REGISTER =
            "biz_receipt_register_head:preReceiptType=221";

    public static final String RECEIPT_TYPE_DELIVERY_NOTICE =
            "biz_receipt_delivery_notice_head:preReceiptType=220";

    public static final String RECEIPT_TYPE_SUPPLIER_CASE =
            "biz_receipt_supplier_case_head:preReceiptType=119";

    public static final String RECEIPT_TYPE_TASK =
            "biz_receipt_task_head:preReceiptType=6101,6201";

    public static final String RECEIPT_TYPE_TASK_REQ =
            "biz_receipt_task_head:preReceiptType=610,620";

    public static final String RECEIPT_TYPE_INSPECT =
            "biz_receipt_inspect_head:preReceiptType=2320,2311";

    public static final String RECEIPT_TYPE_PURCHASE_APPLY =
            "biz_receipt_purchase_apply_head:preReceiptType=401";

    public static final String RECEIPT_TYPE_CONTRACT =
            "biz_receipt_contract_head:preReceiptType=402,403";

    public static final String RECEIPT_TYPE_MAT_APPLY =
            "biz_receipt_mat_apply_head:preReceiptType=185";

    public static final String RECEIPT_TYPE_SERVICE =
            "biz_receipt_service_head:preReceiptType=9500";


    /**
     * 单据类型对应字段取值 - code
     */
    public static final String SOURCE_ATTR_NAME = "receiptCode,createUserId";

    /**
     * 单据类型对应字段取值 - code
     */
    public static final String TARGET_ATTR_NAME = "preReceiptCode,preCreateUserId";

    /* ******************************** 存储类型、仓位KEY ************************************************/
    public static final String TYPE_KEY_CODE = "TYPE_CODE";
    public static final String BIN_KEY_CODE = "BIN_CODE";
    public static final String TYPE_KEY_ID = "TYPE_ID";
    public static final String BIN_KEY_ID = "BIN_ID";

    /* ******************************** 仓位整理模块 ************************************************/
    /**
     * 请求类型-扫描01
     */
    public static final String TYPE_SCAN = "01";

    /**
     * 请求类型-手输02
     */
    public static final String TYPE_MANUAL = "02";


    /**
     * 物料参数-按批次
     */
    public static final String BATCH = "batch";

    /**
     * 物料参数-按物料
     */
    public static final String MATERIAL = "material";

    /**
     * 物料参数-按标签
     */
    public static final String LABEL = "label";

}
