package com.inossem.wms.common.model.masterdata.asset.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资产卡片查询参数
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("资产卡片查询参数")
public class DicAssetSearchPO extends PageCommon {

    @ApiModelProperty("资产编码")
    private String assetCode;
    
    @ApiModelProperty("资产子编号")
    private String assetSubCode;
    
    @ApiModelProperty("资产描述")
    private String assetName;
} 