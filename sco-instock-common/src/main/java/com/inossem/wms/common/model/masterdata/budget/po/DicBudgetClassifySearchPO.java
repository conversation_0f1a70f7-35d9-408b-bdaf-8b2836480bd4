package com.inossem.wms.common.model.masterdata.budget.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15
 */
@ApiModel(value = "预算分类查询入参类", description = "预算分类查询入参类")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DicBudgetClassifySearchPO extends PageCommon {

    @ApiModelProperty(value = "预算分类编码")
    private String budgetClassifyCode;

    @ApiModelProperty(value = "预算分类描述")
    private String budgetClassifyName;

}
