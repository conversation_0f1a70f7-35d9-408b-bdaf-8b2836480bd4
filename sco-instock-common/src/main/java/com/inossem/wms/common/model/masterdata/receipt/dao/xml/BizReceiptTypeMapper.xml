<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.common.model.masterdata.receipt.dao.BizReceiptTypeMapper">

    <!--获取单据类型对应存储区列表-分页-->
    <select id="selectDicReceiptTypePageVOList"
            resultType="com.inossem.wms.common.model.masterdata.receipt.vo.BizReceiptPageVO">
        SELECT
            `id`,
            `receipt_type`,
            `receipt_type_name`,
            `wh_id`,
            `type_id`,
            `section_id`,
            `create_time`,
            `modify_time`,
            `create_user_id`,
            `modify_user_id`
        FROM
            `biz_receipt_type`

        ${ew.customSqlSegment}
    </select>
</mapper>
