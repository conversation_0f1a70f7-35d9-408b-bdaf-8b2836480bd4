package com.inossem.wms.common.mybatisplus;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;

/**
 * 基于MyBatis-plus提供的BaseMapper进行拓展 达到无需编写 mapper.xml 文件，拓展系统自定义功能的目标 此接口中的实现需要
 * 
 * @param <T>
 * <AUTHOR> <<EMAIL>>
 */
public interface WmsBaseMapper<T> extends BaseMapper<T> {

    /**
     * 根据 ID 物理删除
     *
     * @param id 主键ID
     * @return the number of effect rows 影响行数
     */
    int physicalDeleteById(Serializable id);

    /**
     * 删除（根据ID 批量物理删除）
     *
     * @param idList 主键ID列表(不能为 null 以及 empty)
     * @return the number of effect rows 影响行数
     */
    int multiPhysicalDeleteByIdList(@Param(Constants.COLLECTION) Collection<? extends Serializable> idList);

    /**
     * 根据 条件字段Map 物理删除
     *
     * @param columnMap 条件字段Map
     * @return the number of effect rows 影响行数
     */
    int physicalDeleteByMap(@Param(Constants.COLUMN_MAP) Map<String, Object> columnMap);

    /**
     * 根据 queryWrapper 物理删除
     *
     * @param queryWrapper 条件字段包装对象
     * @return the number of effect rows 影响行数
     */
    int physicalDelete(@Param(Constants.WRAPPER) Wrapper<T> queryWrapper);


    /**
     * 根据ids查询出对应表数据，忽略逻辑删除
     * @param idList id列表
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2021-07-29
     */
    List<T> selectByIdsIgnoreDelete(@Param(Constants.COLLECTION) Collection<? extends Serializable> idList);

	/**
	 * 优化根据id批量修改
	 * @param dataList 数据列表
	 * @return
	 */
	int updateBatchByIdOptimize(@Param(Constants.COLLECTION) Collection dataList);

	/**
	 * 优化批量保存
	 * @param dataList 数据列表
	 * @return
	 */
	int saveBatchOptimize(@Param(Constants.COLLECTION) Collection dataList);

}
