package com.inossem.wms.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <p>
 * 越权认证（含参数md5校验、有效期校验、用户权限校验）
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-08-18
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface UltraVires {
}
