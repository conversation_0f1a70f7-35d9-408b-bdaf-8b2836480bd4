package com.inossem.wms.common.model.masterdata.base.init;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据库表
 * 
 * <AUTHOR>
 */
@Data
public class TableBase {

    @ApiModelProperty(value = "所属实例" , example = "BizReceiptInputHead")
    private String tableSchema;
    @ApiModelProperty(value = "所属表" , example = "biz_receipt_input_head")
    private String tableName;
    private List<ColumnBase> columns;
}
