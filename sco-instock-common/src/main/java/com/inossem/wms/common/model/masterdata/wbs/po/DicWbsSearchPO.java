package com.inossem.wms.common.model.masterdata.wbs.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * WBS查询参数
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("WBS查询参数")
public class DicWbsSearchPO extends PageCommon {

    @ApiModelProperty("WBS编码")
    private String wbsCode;
    
    @ApiModelProperty("WBS名称")
    private String wbsName;
    
    @ApiModelProperty("地点")
    private String location;
} 