package com.inossem.wms.common.model.masterdata.mat.info.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizMaterialReturnWaybill对象", description="物资返运运单信息")
@TableName("biz_material_return_waybill")
public class BizMaterialReturnWaybill implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "item表id")
    private Long itemId;

    @ApiModelProperty(value = "运单id")
    private Long billId;

    @ApiModelProperty(value = "序号" , example = "1")
    private String bid;

    @ApiModelProperty(value = "运返箱号")
    private String returnBox;

    @ApiModelProperty(value = "返运数量")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "状态说明")
    private String statusDesc;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String billRemark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
