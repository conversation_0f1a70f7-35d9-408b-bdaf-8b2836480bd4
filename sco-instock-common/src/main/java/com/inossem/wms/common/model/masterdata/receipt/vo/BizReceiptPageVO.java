package com.inossem.wms.common.model.masterdata.receipt.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/27 13:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "单据类型对应存储区对象出参", description = "单据类型对应存储区分页对象出参")
public class BizReceiptPageVO {

    /* ================================= 扩展字段开始 =================================*/


    @ApiModelProperty(value = "单据类型名称", example  = "领料出库", required = true)
    private String receiptTypeI18n;

    /* ================================= 扩展字段结束 =================================*/


    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private String receiptType;

    @ApiModelProperty(value = "单据类型描述")
    private String receiptTypeName;


    @ApiModelProperty(value = "填充属性 - 仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库名称" , example = "英诺森沈阳仓库")
    private String whName;


    @ApiModelProperty(value = "填充属性 - 存储类型编码")
    private String typeCode;

    @ApiModelProperty(value = "填充属性 - 存储区code")
    private String sectionCode;

    @ApiModelProperty(value = "填充属性 - 存储类型名称")
    private String typeName;
    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库号id")
    private Long whId;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "typeCode,typeName")
    @ApiModelProperty(value = "存储类型id")
    private Long typeId;

    @RlatAttr(rlatTableName = "dic_wh_storage_section", sourceAttrName = "sectionCode,sectionName", targetAttrName = "sectionCode,sectionName")
    @ApiModelProperty(value = "存储区id")
    private Long sectionId;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;
    // 存储区名称
    private String sectionName;
}
