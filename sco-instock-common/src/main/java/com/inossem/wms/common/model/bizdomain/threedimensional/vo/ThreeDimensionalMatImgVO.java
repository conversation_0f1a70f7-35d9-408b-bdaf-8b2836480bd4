package com.inossem.wms.common.model.bizdomain.threedimensional.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 仓位物料库存信息
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "仓位物料库存信息", description = "仓位物料库存信息 查询出参")
public class ThreeDimensionalMatImgVO {

    @ApiModelProperty(value = "物料编码" , example = "0000032")
    String matCode;//物料编码

    @ApiModelProperty(value = "物料名称" , example = "扳手")
    String matName; //物料名称

    @ApiModelProperty(value = "批次号" , example = "0000652")
    String batchCode;//批次号

    @ApiModelProperty(value = "图片base" , example = "0000652")
    String imgBase64;//批次号

}
