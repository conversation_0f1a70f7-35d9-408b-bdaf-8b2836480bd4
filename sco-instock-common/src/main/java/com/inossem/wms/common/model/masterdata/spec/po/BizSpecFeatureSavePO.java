package com.inossem.wms.common.model.masterdata.spec.po;

import java.io.Serializable;

import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecFeatureDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 特性表新增/修改入参类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "特性表新增/修改入参类", description = "特性表新增/修改入参类")
public class BizSpecFeatureSavePO implements Serializable {

    private static final long serialVersionUID = -6661152208172473789L;

    @ApiModelProperty(value = "特性信息入参")
    private BizSpecFeatureDTO bizSpecFeatureInfo;
}
