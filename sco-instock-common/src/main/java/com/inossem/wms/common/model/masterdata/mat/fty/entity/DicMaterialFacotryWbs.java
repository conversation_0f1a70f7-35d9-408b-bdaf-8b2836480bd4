package com.inossem.wms.common.model.masterdata.mat.fty.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DicMaterialFacotryWbs对象", description="")
@TableName("dic_material_facotry_wbs")
public class DicMaterialFacotryWbs implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "移动平均价")
    private BigDecimal moveAvgPrice;

    private String specStock;

    private String specStockCode;

    @ApiModelProperty(value = "WBS总价")
    private BigDecimal totalPrice;

    private BigDecimal totalQty;
}
