package com.inossem.wms.common.model.masterdata.facility.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 设施管理表传输对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicFacility传输对象", description = "设施管理表传输对象")
public class DicFacilityDTO implements Serializable {

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    /* ********************** 扩展字段结束 *************************/

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("设施编码")
    private String facilityCode;

    @ApiModelProperty("设施描述")
    private String facilityName;

    @ApiModelProperty("设施位置描述")
    private String locationDes;

    @ApiModelProperty("设施费用（USD/天）")
    private BigDecimal cost;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("预订日期")
    private Date targetDate;

    @TableLogic
    @ApiModelProperty("是否删除【1是，0否】")
    private Integer isDelete;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty("创建人id")
    private Long createUserId;

    @ApiModelProperty("修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "合同id(biz_receipt_contract_head表id)")
    private Long contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

}
