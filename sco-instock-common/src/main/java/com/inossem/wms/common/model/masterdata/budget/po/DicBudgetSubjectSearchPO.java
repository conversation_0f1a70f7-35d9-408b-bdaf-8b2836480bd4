package com.inossem.wms.common.model.masterdata.budget.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15
 */
@ApiModel(value = "预算科目查询入参类", description = "预算科目查询入参类")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DicBudgetSubjectSearchPO extends PageCommon {


    @ApiModelProperty(value = "预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty(value = "预算科目描述")
    private String budgetSubjectName;
}
