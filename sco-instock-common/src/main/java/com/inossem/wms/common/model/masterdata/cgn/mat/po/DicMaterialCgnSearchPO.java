package com.inossem.wms.common.model.masterdata.cgn.mat.po;


import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;

/**
 * 项目物料码管理
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@TableName("dic_material_cgn")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "项目物料码管理查询")
public class DicMaterialCgnSearchPO extends PageCommon {

    @ApiModelProperty(value = "项目物料码")
    private String cgnMatCode;

    @ApiModelProperty(value = "项目物料简称")
    private String cgnMatName;
}
