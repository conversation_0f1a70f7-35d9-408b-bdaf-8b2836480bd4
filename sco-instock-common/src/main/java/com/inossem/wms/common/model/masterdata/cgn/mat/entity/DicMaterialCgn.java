package com.inossem.wms.common.model.masterdata.cgn.mat.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Author: zhaohaitao
 * @Date: 2024-07-15
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicMaterialCgn", description = "项目物料码管理")
public class DicMaterialCgn {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "项目物料码/物资编码")
    private String cgnMatCode;

    @ApiModelProperty(value = "项目物料简称")
    private String cgnMatName;

    @ApiModelProperty(value = "裕量计算分类id")
    private Long marginCategoryId;


    @TableLogic
    @ApiModelProperty(value = "删除标识 0:未删除；1:已删除")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;


}
