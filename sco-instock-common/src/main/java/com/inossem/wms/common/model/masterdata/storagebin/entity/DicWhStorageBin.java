package com.inossem.wms.common.model.masterdata.storagebin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 仓位表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicWhStorageBin对象", description = "仓位表")
@TableName("dic_wh_storage_bin")
public class DicWhStorageBin implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @ApiModelProperty(value = "仓位" , example = "00")
    private String binCode;

    @ApiModelProperty(value = "存储区" , example = "s008")
    private Long sectionId;

    @ApiModelProperty(value = "入库冻结标识。未冻结0， 盘点冻结1 ，手动冻结2  wcs冻结3" , example = "0")
    private Integer freezeInput;

    @ApiModelProperty(value = "出库冻结标识。未冻结0， 盘点冻结1 ，手动冻结2  wcs冻结3" , example = "0")
    private Integer freezeOutput;

    @ApiModelProperty(value = "冻结原因" , example = "0")
    private String freezeReason;

    @ApiModelProperty(value = "空仓位标识，0未使用，空，1已使用，不为空" , example = "0")
    private Integer isEmpty;

    @ApiModelProperty(value = "是否默认仓位【1是，0否】" , example = "0")
    private Integer isDefault;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "是否为核岛工具间【0：否；1：是】")
    private Integer isNuclearIslandToolRoom;
    private Long corpId;
    private Long ftyId;
    private String groupShelfNo;
    private String groupLineNo;
    private String groupColumnNo;
    private String groupBinNo;

    @ApiModelProperty(value = "仓位承重（KG）")
    private BigDecimal weight;

    @ApiModelProperty(value = "是否超重")
    private String overWeight;
}
