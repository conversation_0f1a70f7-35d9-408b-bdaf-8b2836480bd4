package com.inossem.wms.common.model.masterdata.margin.po;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

import java.math.BigDecimal;

/**
 * 裕量分类
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "裕量分类导入")
public class DicMarginCategoryImportPO {

    @ExcelProperty(value = "裕量计算分类", index = 0)
    @ApiModelProperty(value = "裕量计算分类编码")
    private String marginCategoryCode;

    @ExcelProperty(value = "描述", index = 1)
    @ApiModelProperty(value = "裕量描述")
    private String marginCategoryDesc;

    @ExcelProperty(value = "工作裕量", index = 2)
    @ApiModelProperty(value = "工作裕量")
    private BigDecimal marginFactor;

    @ExcelProperty(value = "最小值", index = 3)
    @ApiModelProperty(value = "工作裕量最小值")
    private BigDecimal marginFactorMin;

    @ExcelProperty(value = "最大值", index = 4)
    @ApiModelProperty(value = "工作裕量最大值")
    private BigDecimal marginFactorMax;

    @ExcelProperty(value = "应急裕量", index =5)
    @ApiModelProperty(value = "应急裕量")
    private BigDecimal marginFactorEmergency;

    @ExcelProperty(value = "最小值", index = 6)
    @ApiModelProperty(value = "应急裕量最小值")
    private BigDecimal marginFactorEmergencyMin;

    @ExcelProperty(value = "最大值", index = 7)
    @ApiModelProperty(value = "应急裕量最大值")
    private BigDecimal marginFactorEmergencyMax;

    @ExcelProperty(value = "辅助裕量", index = 8)
    @ApiModelProperty(value = "辅助裕量")
    private BigDecimal marginFactorAssist;

    @ExcelProperty(value = "最小值", index = 9)
    @ApiModelProperty(value = "辅助裕量最小值")
    private BigDecimal marginFactorAssistMin;

    @ExcelProperty(value = "最大值", index = 10)
    @ApiModelProperty(value = "辅助裕量最大值")
    private BigDecimal marginFactorAssistMax;

    @ExcelProperty(value = "圆整方向", index = 11)
    @ApiModelProperty(value = "圆整方向")
    private String roundingDirection;

    @ExcelProperty(value = "圆整增量", index = 12)
    @ApiModelProperty(value = "圆整增量")
    private String roundingIncrement;

    public String getMarginCategoryCode() {
        return marginCategoryCode;
    }

    public void setMarginCategoryCode(String marginCategoryCode) {
        this.marginCategoryCode = marginCategoryCode;
    }

    public String getMarginCategoryDesc() {
        return marginCategoryDesc;
    }

    public void setMarginCategoryDesc(String marginCategoryDesc) {
        this.marginCategoryDesc = marginCategoryDesc;
    }

    public BigDecimal getMarginFactor() {
        return marginFactor;
    }

    public void setMarginFactor(BigDecimal marginFactor) {
        this.marginFactor = marginFactor;
    }

    public BigDecimal getMarginFactorMin() {
        return marginFactorMin;
    }

    public void setMarginFactorMin(BigDecimal marginFactorMin) {
        this.marginFactorMin = marginFactorMin;
    }

    public BigDecimal getMarginFactorMax() {
        return marginFactorMax;
    }

    public void setMarginFactorMax(BigDecimal marginFactorMax) {
        this.marginFactorMax = marginFactorMax;
    }

    public BigDecimal getMarginFactorEmergency() {
        return marginFactorEmergency;
    }

    public void setMarginFactorEmergency(BigDecimal marginFactorEmergency) {
        this.marginFactorEmergency = marginFactorEmergency;
    }

    public BigDecimal getMarginFactorEmergencyMin() {
        return marginFactorEmergencyMin;
    }

    public void setMarginFactorEmergencyMin(BigDecimal marginFactorEmergencyMin) {
        this.marginFactorEmergencyMin = marginFactorEmergencyMin;
    }

    public BigDecimal getMarginFactorEmergencyMax() {
        return marginFactorEmergencyMax;
    }

    public void setMarginFactorEmergencyMax(BigDecimal marginFactorEmergencyMax) {
        this.marginFactorEmergencyMax = marginFactorEmergencyMax;
    }

    public BigDecimal getMarginFactorAssist() {
        return marginFactorAssist;
    }

    public void setMarginFactorAssist(BigDecimal marginFactorAssist) {
        this.marginFactorAssist = marginFactorAssist;
    }

    public BigDecimal getMarginFactorAssistMin() {
        return marginFactorAssistMin;
    }

    public void setMarginFactorAssistMin(BigDecimal marginFactorAssistMin) {
        this.marginFactorAssistMin = marginFactorAssistMin;
    }

    public BigDecimal getMarginFactorAssistMax() {
        return marginFactorAssistMax;
    }

    public void setMarginFactorAssistMax(BigDecimal marginFactorAssistMax) {
        this.marginFactorAssistMax = marginFactorAssistMax;
    }

    public String getRoundingDirection() {
        return roundingDirection;
    }

    public void setRoundingDirection(String roundingDirection) {
        this.roundingDirection = roundingDirection;
    }

    public String getRoundingIncrement() {
        return roundingIncrement;
    }

    public void setRoundingIncrement(String roundingIncrement) {
        this.roundingIncrement = roundingIncrement;
    }
}
