package com.inossem.wms.common.mybatisplus;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDataFill;
import com.inossem.wms.common.util.UtilSequence;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilMetadata;
import com.inossem.wms.common.util.UtilReflect;

import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * 基础数据包装类
 * 
 * <AUTHOR>
 * @date 2021-03-24
 */
@Slf4j
public class BaseDataWrap<M extends WmsBaseMapper<T>, T> extends ServiceImpl<M, T> {

	/**
	 * 填充不允许为空的属性时忽略设置默认值的属性列表
	 */
	private List<String> fillCannotBeNullFieldIgnoreFiledList = new LinkedList(){{add("id"); add("isDelete"); add("createTime"); add("modifyTime"); add("createUserId"); add("createUserCode"); add("createUserName"); add("modifyUserId");}};

	/**
	 * 填充TableField注解指定填充的属性时插入场景需要处理的属性名列表
	 */
	private List<String> fillTableFieldInsertFieldList = new LinkedList(){{ add("createUserId"); add("createUserCode"); add("createUserName"); add("createTime"); add("modifyUserId"); add("modifyTime"); }};

	/**
	 * 填充TableField注解指定填充的属性时修改场景需要处理的属性名列表
	 */
	private List<String> fillTableFieldUpdateFieldList = new LinkedList(){{ add("modifyUserId"); add("modifyTime"); }};


	/**
	 * 保存entity
	 *
	 */
	@Override
	public boolean save(T entity) {

		// 填充插入场景时需要填充的数据
		this.fillField(Arrays.asList(entity), FieldFill.INSERT);

		return super.save(entity);
	}

	/**
	 * 保存entity
	 *
	 */
	@Override
	public boolean updateById(T entity) {

		// 填充修改场景时需要填充的数据
		this.fillField(Arrays.asList(entity), FieldFill.UPDATE);

		return super.updateById(entity);
	}

	/**
     * 保存DTO数据
     * 
     * @param dto 数据对象
     */
    public boolean saveDto(Object dto) {
        // dto转entity
        T entity = UtilBean.newInstance(dto, this.getEntityClass());
        // 保存
        boolean result = this.save(entity);
        // 回填主键数据
        this.backFillPkData(entity, dto);
        return result;
    }

    /**
     * 根据ids查询出实际数据，忽略逻辑删除
     */
    public List<T> selectByIdsIgnoreDelete(Collection<? extends Serializable> idList){
        return this.getBaseMapper().selectByIdsIgnoreDelete(idList);
    }


    /**
     * 保存或修改DTO数据
     *
     * @param dto 数据对象
     */
    public boolean saveOrUpdateDto(Object dto) {
        // dto转entity
        T entity = UtilBean.newInstance(dto, this.getEntityClass());
        // 保存
        boolean result = this.saveOrUpdate(entity);
        // 回填主键数据
        this.backFillPkData(entity, dto);
        return result;
    }

    /**
     * 保存或修改DTO数据
     *
     * @param dto 数据对象
     * <AUTHOR> <<EMAIL>>
     */
    public boolean saveOrUpdateDto(Object dto, CurrentUser user) {
        // dto转entity
        T entity = UtilBean.newInstance(dto, this.getEntityClass());

        /* set 创建人和修改人 */
        try {
            if (UtilReflect.getValueByField("id", Long.class) == null) {
                UtilReflect.setValueByField(entity, "createUserId", user.getCreateUserId());
            }
            UtilReflect.setValueByField(entity, "modifyUserId", user.getCreateUserId());
        } catch (Exception e) {
            log.debug("保存dto对象自动处理create_user_id/modify_user_id字段过程被跳过, {}缺少字段定义", entity.getClass().toString());
        }
        // 保存
        boolean result = this.saveOrUpdate(entity);
        // 回填主键数据
        this.backFillPkData(entity, dto);
        return result;
    }

//    /**
//     * 保存或修改DTO数据
//     *
//     * @param dto 数据对象
//     * @param updateWrapper wrapper对象
//     */
//    public boolean saveOrUpdateDto(Object dto, Wrapper<T> updateWrapper) {
//        // dto转entity
//        T entity = UtilBean.newInstance(dto, this.getEntityClass());
//        // 保存
//        boolean result = this.saveOrUpdate(entity, updateWrapper);
//        // 回填主键数据
//        this.backFillPkData(entity, dto);
//        return result;
//    }

    /**
     * 批量保存DTO数据
     * 
     * @param dtoList 数据对象集合
     */
    public <DTO> boolean saveBatchDto(List<DTO> dtoList) {
//        return this.saveBatchDto(dtoList, IService.DEFAULT_BATCH_SIZE);
		return this.saveBatchOptimize(dtoList);
    }

    /**
     * 批量保存DTO数据
     * 
     * @param dtoList 数据对象集合
     * @param batchSize 数量
     */
    public <DTO> boolean saveBatchDto(List<DTO> dtoList, int batchSize) {
//        // dtoList转entityList
//        List<T> entityList = UtilCollection.toList(dtoList, this.getEntityClass());
//        // 保存
//        boolean result = this.saveBatch(entityList, batchSize);
//
//        // 遍历回填新增时自动生成的主键数据
//        for (int i = 0; i < entityList.size(); i++) {
//            this.backFillPkData(entityList.get(i), dtoList.get(i));
//        }
//        return result;

		return this.saveBatchOptimize(dtoList, batchSize);
    }

    /**
     * 批量保存或修改DTO数据
     * 
     * @param dtoList 数据对象集合
     */
    public <DTO> boolean saveOrUpdateBatchDto(List<DTO> dtoList) {
//        return this.saveOrUpdateBatchDto(dtoList, IService.DEFAULT_BATCH_SIZE);
		return this.saveOrUpdateBatchOptimize(dtoList);
    }

    /**
     * 批量保存或修改DTO数据
     * 
     * @param dtoList 数据对象集合
     * @param batchSize 长度
     */
    public <DTO extends Object> boolean saveOrUpdateBatchDto(List<DTO> dtoList, int batchSize) {
//        // dtoList转entityList
//        List<T> entityList = UtilCollection.toList(dtoList, this.getEntityClass());
//        // 保存
//        boolean result = this.saveOrUpdateBatch(entityList, batchSize);
//        // 遍历回填新增时自动生成的主键数据
//        for (int i = 0; i < entityList.size(); i++) {
//            this.backFillPkData(entityList.get(i), dtoList.get(i));
//        }
//        return result;
		return this.saveOrUpdateBatchOptimize(dtoList, batchSize);
    }

    /**
     * 修改DTO数据
     *
     * @param dto 数据对象
     * @param updateWrapper wrapper对象
     */
    public boolean updateDto(Object dto, Wrapper<T> updateWrapper) {
        // dto转entity
        T entity = UtilBean.newInstance(dto, this.getEntityClass());
        // 执行修改
        return this.update(entity, updateWrapper);
    }

    /**
     * 根据主键修改DTO数据
     *
     * @param dto 数据对象
     */
    public boolean updateDtoById(Object dto) {
        // dto转entity
        T entity = UtilBean.newInstance(dto, this.getEntityClass());
        // 执行修改
        return this.updateById(entity);
    }

    /**
     * 根据主键批量修改DTO数据
     *
     * @param dtoList 数据对象集合
     */
    public <DTO> boolean updateBatchDtoById(List<DTO> dtoList) {
//        return this.updateBatchDtoById(dtoList, IService.DEFAULT_BATCH_SIZE);
		return this.updateBatchByIdOptimize(dtoList);
    }

    /**
     * 根据主键批量修改DTO数据
     *
     * @param dtoList 数据对象集合
     */
    public <DTO> boolean updateBatchDtoById(List<DTO> dtoList, int batchSize) {
//        // dtoList转entityList
//        List<T> entityList = UtilCollection.toList(dtoList, this.getEntityClass());
//        // 执行修改
//        return this.updateBatchById(entityList, batchSize);

        return this.updateBatchByIdOptimize(dtoList, batchSize);
    }

    /**
     * 回填主键数据 把entity中的主键数据回填到dto对象中
     * 
     * @param entity entity对象
     * @param dto dto对象
     */
    private void backFillPkData(T entity, Object dto) {
        // 取得主键属性名
        String pkAttrName = UtilMetadata.getPkAttrName(this.getEntityClass());
        // 回填主键数据
        this.backFillPkData(entity, dto, pkAttrName);
    }

    /**
     * 回填主键数据
     * 
     * @param entity entity对象
     * @param dto dto对象
     * @param pkAttrName 主键属性名
     */
    private void backFillPkData(T entity, Object dto, String pkAttrName) {
        // 把entity中的主键数据回填到dto对象中
        UtilReflect.setValueByField(dto, pkAttrName, UtilReflect.getValueByField(pkAttrName, entity));
    }

    /**
     * 根据id物理删除
     * 
     * @param id 主键id
     * @return true when operation is success
     * <AUTHOR> <<EMAIL>>
     */
    public boolean physicalDeleteById(Serializable id) {
        return SqlHelper.retBool(this.getBaseMapper().physicalDeleteById(id));
    }

    /**
     * 根据id列表物理删除
     * 
     * @param idList id列表
     * @return true when operation is success
     * <AUTHOR> <<EMAIL>>
     */
    public boolean multiPhysicalDeleteByIdList(Collection<? extends Serializable> idList) {
        return UtilCollection.isNotEmpty(idList) && SqlHelper.retBool(this.getBaseMapper().multiPhysicalDeleteByIdList(idList));
    }

    /**
     * @param columnMap 字段Map
     * @return true when operation is success
     * <AUTHOR> <<EMAIL>>
     */
    public boolean physicalDeleteByMap(Map<String, Object> columnMap) {
        Assert.notEmpty(columnMap, "error: columnMap must not be empty");
        return SqlHelper.retBool(this.getBaseMapper().physicalDeleteByMap(columnMap));
    }

    /**
     * @param queryWrapper wrapper对象
     * <AUTHOR> <<EMAIL>>
     */
    public boolean physicalDelete(Wrapper<T> queryWrapper) {
        return SqlHelper.retBool(this.getBaseMapper().physicalDelete(queryWrapper));
    }

	/**
	 * 优化批量保存
	 *
	 * @param dataList 要保存的数据列表
	 * @return boolean
	 */
	public boolean saveBatchOptimize(Collection dataList) {
		return this.saveBatchOptimize(dataList, 100);
	}

	/**
	 * 优化批量保存
	 *
	 * @param dataList 要保存的数据列表
	 * @param batchSize 批量数
	 * @return boolean
	 */
	public boolean saveBatchOptimize(Collection<?> dataList, int batchSize) {

		if(UtilCollection.isEmpty(dataList)){
			return false;
		}

		// 填充插入场景时需要填充的数据
		this.fillField(dataList, FieldFill.INSERT);

		if(dataList.size() <=  batchSize){
			return SqlHelper.retBool(this.getBaseMapper().saveBatchOptimize(dataList));
		}

		// 如果数据总条数超过批量数，则分多次执行
		for(int i = 0; i < (dataList.size() / batchSize + 1); i++){
			Collection batchData = dataList.stream().skip(i * batchSize).limit(batchSize).collect(Collectors.toList());

			if(UtilCollection.isEmpty(batchData)){
				continue;
			}

			this.getBaseMapper().saveBatchOptimize(batchData);
		}

		return true;
	}

	/**
	 * 优化根据id批量修改
	 *
	 * @param dataList 要修改的数据列表
	 * @return
	 */
	public boolean updateBatchByIdOptimize(Collection dataList) {
		return this.updateBatchByIdOptimize(dataList, 100);
	}

	/**
	 * 优化根据id批量修改
	 *
	 * @param dataList 要修改的数据列表
	 * @param batchSize 批量数
	 * @return boolean
	 */
	public boolean updateBatchByIdOptimize(Collection<?> dataList, int batchSize) {

		if(UtilCollection.isEmpty(dataList)){
			return false;
		}

		// 填充修改场景时需要填充的数据
		this.fillField(dataList, FieldFill.UPDATE);

		if(dataList.size() <=  batchSize){
			return SqlHelper.retBool(this.getBaseMapper().updateBatchByIdOptimize(dataList));
		}

		// 如果数据总条数超过批量数，则分多次执行
		for(int i = 0; i < (dataList.size() / batchSize + 1); i++){
			Collection batchData = dataList.stream().skip(i * batchSize).limit(batchSize).collect(Collectors.toList());

			if(UtilCollection.isEmpty(batchData)){
				continue;
			}

			this.getBaseMapper().updateBatchByIdOptimize(batchData);
		}

		return true;
	}

	/**
	 * 优化批量保存或修改
	 *
	 * @param dataList 要修改的数据列表
	 * @return boolean
	 */
	public boolean saveOrUpdateBatchOptimize(Collection dataList) {
		return this.saveOrUpdateBatchOptimize(dataList, 100);
	}

	/**
	 * 优化批量保存或修改
	 *
	 * @param dataList 要修改的数据列表
	 * @param batchSize 要修改的数据列表
	 * @return boolean
	 */
	public boolean saveOrUpdateBatchOptimize(Collection<?> dataList, int batchSize) {

		if(UtilCollection.isEmpty(dataList)){
			return false;
		}

		// 表信息
		TableInfo tableInfo = TableInfoHelper.getTableInfo(this.getEntityClass());

		// 主键信息
		String keyProperty = tableInfo.getKeyProperty();

		// 主键列表
		Collection keyList = dataList.stream().filter(obj -> UtilReflect.getValueByFieldNullReturnNull(keyProperty, obj) != null).map(obj -> UtilReflect.getValueByFieldNullReturnNull(keyProperty, obj)).collect(Collectors.toList());

		// 主键数据列表
		Collection<?> keyDataList = UtilCollection.isEmpty(keyList) ? null : this.selectByIdsIgnoreDelete(keyList);

		if(UtilCollection.isEmpty(keyDataList)){
			keyDataList = new LinkedList<>();
		}

		// 在数据库中存在的主键列表
		Collection keyExistList = keyDataList.stream().map(obj -> UtilReflect.getValueByFieldNullReturnNull(keyProperty, obj)).collect(Collectors.toList());

		// 应该进行保存的数据
		Collection saveDataList = dataList.stream().filter(obj -> UtilReflect.getValueByFieldNullReturnNull(keyProperty, obj) == null || !keyExistList.contains(UtilReflect.getValueByFieldNullReturnNull(keyProperty, obj))).collect(Collectors.toList());

		// 应该进行修改的数据
		Collection updateDataList = dataList.stream().filter(obj -> UtilReflect.getValueByFieldNullReturnNull(keyProperty, obj) != null && keyExistList.contains(UtilReflect.getValueByFieldNullReturnNull(keyProperty, obj))).collect(Collectors.toList());

		// 执行批量保存
		this.saveBatchOptimize(saveDataList, batchSize);

		// 执行批量修改
		this.updateBatchByIdOptimize(updateDataList, batchSize);

		return true;
	}

	/**
	 * 根据id获取指定类型DTO对象
	 *
	 * @param dtoClass
	 * @param id
	 * @param <DTO>
	 * @return
	 */
	public <DTO> DTO getDtoById(Class<DTO> dtoClass, Serializable id) {

		T entity = super.getById(id);

		if (entity == null) {
			return null;
		}

		if (this.getEntityClass().getName().equals(dtoClass.getName())) {
			return (DTO) entity;
		}

		DTO returnValue = UtilBean.newInstance(entity, dtoClass);

		UtilDataFill.fillAttr(returnValue);

		return returnValue;
	}

	/**
	 * 根据id列表获取DTO列表
	 *
	 * @param dtoClass     dto类型
	 * @param queryWrapper 查询条件
	 * @return
	 */
	public <DTO> List<DTO> listDto(Class<DTO> dtoClass, Wrapper<T> queryWrapper) {
		// 查询出来的entity列表
		List<T> entityList = super.list(queryWrapper);

		if (this.getEntityClass().getName().equals(dtoClass.getName())) {
			return (List<DTO>) entityList;
		}

		// entity列表转换为dto列表
		List<DTO> dtoList = UtilCollection.toList(entityList, dtoClass);

		UtilDataFill.fillAttr(dtoList);

		return dtoList;
	}

	/**
	 * 根据id列表获取DTO列表（指定类型填充）
	 *
	 * @param dtoClass     dto类型
	 * @param queryWrapper 查询条件
	 * @param dataFillType 填充类型
	 * @return
	 */
	public <DTO> List<DTO> listDto(Class<DTO> dtoClass, Wrapper<T> queryWrapper, EnumDataFillType dataFillType) {
		// 查询出来的entity列表
		List<T> entityList = super.list(queryWrapper);

		if (this.getEntityClass().getName().equals(dtoClass.getName())) {
			return (List<DTO>) entityList;
		}

		// entity列表转换为dto列表
		List<DTO> dtoList = UtilCollection.toList(entityList, dtoClass);

		UtilDataFill.fillType(dataFillType, dtoList);

		return dtoList;
	}


	/**
	 * 根据id列表获取DTO列表
	 *
	 * @param dtoClass dto类型
	 * @param idList
	 * @return
	 */
	public <DTO> List<DTO> listDtoByIds(Class<DTO> dtoClass, Collection<? extends Serializable> idList) {

		// 通过数据库查询获取entity列表
		List<T> dbDataList = super.listByIds(idList);

		if (this.getEntityClass().getName().equals(dtoClass.getName())) {
			return (List<DTO>) dbDataList;
		}

		// entity列表转换为dto列表
		List<DTO> dbDtoDataList = UtilCollection.toList(dbDataList, dtoClass);

		UtilDataFill.fillAttr(dbDtoDataList);

		return dbDtoDataList;
	}

	/**
	 * 根据id列表获取DTO列表(指定类型填充)
	 *
	 * @param dtoClass dto类型
	 * @param idList
	 * @param dataFillType  填充类型
	 * @return
	 */
	public <DTO> List<DTO> listDtoByIds(Class<DTO> dtoClass, Collection<? extends Serializable> idList, EnumDataFillType dataFillType) {

		// 通过数据库查询获取entity列表
		List<T> dbDataList = super.listByIds(idList);

		if (this.getEntityClass().getName().equals(dtoClass.getName())) {
			return (List<DTO>) dbDataList;
		}

		// entity列表转换为dto列表
		List<DTO> dbDtoDataList = UtilCollection.toList(dbDataList, dtoClass);

		UtilDataFill.fillType(dataFillType, dbDtoDataList);

		return dbDtoDataList;
	}

	/**
	 * 填充属性
	 *
	 * @param dataList 数据列表
	 * @param fieldFill 填充场景
	 */
	private void fillField(Collection<?> dataList, FieldFill fieldFill){

		// 当前entity的所有属性
		List<Field> fieldList = UtilReflect.getAllFields(this.getEntityClass());

		if(UtilCollection.isEmpty(fieldList)){
			return;
		}

		// 数据对象的class
		Class dataClass =  dataList.iterator().next().getClass();

		// 遍历entity属性列表，找出需要填充的属性，分别进行填充
		for(Field field : fieldList){

			field.setAccessible(true);

			// 填充TableField注解指定填充的属性
			this.fillTableField(dataList, dataClass, field, fieldFill);

			// 插入时填充主键数据、逻辑删除属性数据
			if(FieldFill.INSERT.equals(fieldFill)){
				// 填充TableId注解指定的属性值
				this.fillTableId(dataList, dataClass, field);

				// 填充TableLogic注解指定的属性值
				this.fillTableLogic(dataList, dataClass, field);

				// 填充不允许为空的属性
				this.fillCannotBeNullField(dataList, dataClass, field);
			}
		}
	}

	/**
	 * 填充TableId注解指定的属性值
	 *
	 * @param dataList 数据列表
	 * @param dataClass 数据对象class
	 * @param field entity对应属性
	 */
	private void fillTableId(Collection<?> dataList, Class<?> dataClass, Field field){
		// 取得注解信息
		TableId annation = field.getAnnotation(TableId.class);

		if (annation == null) {
			return ;
		}

		// 要填充的属性
		Field fillField = UtilReflect.getFieldNullReturnNull(field.getName(), dataClass);

		// 如果entity中指定要填充的属性在当前数据对象中不存在，则直接返回，放弃填充
		if(fillField == null){
			return;
		}

		// 遍历数据列表，填充数据
		for(Object object : dataList){
			// 要填充的属性值
			Object value = UtilReflect.getValueByField(fillField, object);

			// 如果要填充的属性值不为空，直接跳过
			if(value != null){
				continue;
			}

			// 属性值填充
			UtilReflect.setValueByField(object, fillField, UtilSequence.nextId());
		}
	}

	/**
	 * 填充TableLogic注解指定的属性值
	 *
	 * @param dataList 数据列表
	 * @param dataClass 数据对象class
	 * @param field entity对应属性
	 */
	private void fillTableLogic(Collection<?> dataList, Class<?> dataClass, Field field){
		// 取得注解信息
		TableLogic annation = field.getAnnotation(TableLogic.class);

		if (annation == null) {
			return ;
		}
		// 表信息
		TableInfo tableInfo = TableInfoHelper.getTableInfo(this.getEntityClass());

		// 要填充的属性
		Field fillField = UtilReflect.getFieldNullReturnNull(field.getName(), dataClass);

		// 如果entity中指定要填充的属性在当前数据对象中不存在，则直接返回，放弃填充
		if(fillField == null){
			return;
		}

		// 遍历数据列表，填充数据
		for(Object object : dataList){
			// 要填充的属性值
			Object value = UtilReflect.getValueByField(fillField, object);

			// 如果要填充的属性值不为空，直接跳过
			if(value != null){
				continue;
			}

			if (Long.class.getName().equals(fillField.getType().getName())) {
				// Long类型属性值填充
				UtilReflect.setValueByField(object, fillField, Long.parseLong(tableInfo.getLogicDeleteFieldInfo().getLogicNotDeleteValue()));
			} else if (Integer.class.getName().equals(fillField.getType().getName())) {
				// Integer类型属性值填充
				UtilReflect.setValueByField(object, fillField, Integer.parseInt(tableInfo.getLogicDeleteFieldInfo().getLogicNotDeleteValue()));
			}
		}
	}

	/**
	 * 填充不允许为空的属性
	 *
	 * 数据库中设置了某些字段不允许为空，但是entity中未能体现该信息
	 * 因此默认所有的属性都设置默认值，都不允许为null
	 * 后续经过元数据处理，entity中注解标记不允许为空的属性，再细化处理
	 *
	 * @param dataList 数据列表
	 * @param dataClass 数据对象class
	 * @param field entity对应属性
	 */
	private void fillCannotBeNullField(Collection<?> dataList, Class dataClass, Field field){

		// 如果是忽略列表中的属性，则不处理，该列表中的属性由其他功能处理
		if(fillCannotBeNullFieldIgnoreFiledList.contains(field.getName())){
			return;
		}

		// 要填充的属性
		Field fillField = UtilReflect.getFieldNullReturnNull(field.getName(), dataClass);

		// 如果entity中指定要填充的属性在当前数据对象中不存在，则直接返回，放弃填充
		if(fillField == null){
			return;
		}

		// 遍历数据列表，填充数据
		for(Object object : dataList){
			Object value = null;

			try {
				// 反射对象属性值
				value = UtilReflect.getValueByField(fillField, object);
			} catch (Exception e){
				continue;
			}

			// 如果属性值有值，则跳过不处理
			if (value != null) {
				continue;
			}

			// 依据于属性类型，分别设置对应类型的默认值
			if (String.class.equals(fillField.getType())) {
				UtilReflect.setValueByField(object, fillField, StringUtils.EMPTY);
			} else if(Date.class.equals(fillField.getType())){
				// 日期类型属性不填充默认值，如有必要则在业务代码中手动set
			} else if(Boolean.class.equals(fillField.getType())){
				UtilReflect.setValueByField(object, fillField, Boolean.FALSE);
			} else if(Long.class.equals(fillField.getType())){
				UtilReflect.setValueByField(object, fillField, 0L);
			} else if(Integer.class.equals(fillField.getType())){
				UtilReflect.setValueByField(object, fillField, 0);
			} else if(BigDecimal.class.equals(fillField.getType())){
				UtilReflect.setValueByField(object, fillField, BigDecimal.ZERO);
			}
		}
	}

	/**
	 * 填充TableField注解指定填充的属性
	 *
	 * @param dataList 数据列表
	 * @param dataClass 数据对象class
	 * @param field entity对应属性
	 * @param fieldFill 填充场景
	 */
	private void fillTableField(Collection<?> dataList, Class<?> dataClass, Field field, FieldFill fieldFill){
//		// 取得注解信息
//		TableField annation = field.getAnnotation(TableField.class);
//
//		if (annation == null) {
//			return ;
//		}
//
//		// 是否填充（注解中fill值为【FieldFill.INSERT_UPDATE】会填充、或者注解中fill值符合当前填充场景也会填充）
//		boolean isFill = FieldFill.INSERT_UPDATE.equals(annation.fill()) ? true : fieldFill.equals(annation.fill());
//
//		// 如果未标记填充则直接返回
//		if(!isFill){
//			return;
//		}

		// 插入场景处理创建人、创建时间、修改人、修改时间，修改场景处理修改人、修改时间
		if(FieldFill.INSERT.equals(fieldFill)){
			if(!fillTableFieldInsertFieldList.contains(field.getName())){
				return;
			}
		} else if(FieldFill.UPDATE.equals(fieldFill)){
			if(!fillTableFieldUpdateFieldList.contains(field.getName())){
				return;
			}
		} else {
			return;
		}

		// 要填充的属性
		Field fillField = UtilReflect.getFieldNullReturnNull(field.getName(), dataClass);

		// 如果entity中指定要填充的属性在当前数据对象中不存在，则直接返回，放弃填充
		if(fillField == null){
			return;
		}

		// 要填充的值
		Object fillValue = null;

		// 目前实际使用中需要填充的值包括（createUserId、createUserCode、createUserName、modifyUserId、createTime、modifyTime）
		if(fillField.getName().endsWith("UserId")){
			CurrentUser user = UtilCurrentContext.getCurrentUser();
			fillValue = user != null ? user.getId() : 0;
		} else if(fillField.getName().endsWith("UserCode")){
			CurrentUser user = UtilCurrentContext.getCurrentUser();
			fillValue = user != null ? user.getUserCode() : StringUtils.EMPTY;
		} else if(fillField.getName().endsWith("UserName")){
			CurrentUser user = UtilCurrentContext.getCurrentUser();
			fillValue = user != null ? user.getUserName() : StringUtils.EMPTY;
		} else if(fillField.getName().endsWith("Time")){
			fillValue = new Date();
		}

		// 遍历数据列表，填充数据
		for(Object object : dataList){
			// 要填充的属性值
			Object value = UtilReflect.getValueByField(fillField, object);

			// 如果要填充的属性值不为空，直接跳过(修改时间属性必更新，防止修改时传入了一个从表里查出来的旧的修改时间，导致修改后修改时间无变化的情况)
			if(value != null && !"modifyTime".equals(fillField.getName())){
				continue;
			}

			// 属性值填充
			UtilReflect.setValueByField(object, fillField, fillValue);
		}
	}
}
