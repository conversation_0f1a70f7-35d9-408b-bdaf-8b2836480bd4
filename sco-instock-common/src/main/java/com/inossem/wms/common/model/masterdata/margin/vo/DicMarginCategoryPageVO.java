package com.inossem.wms.common.model.masterdata.margin.vo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
/**
* 裕量分类
*
* <AUTHOR>
* @since 2024-07-16
*/
@Data
@TableName("dic_margin_category")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="裕量分类分页列表")
public class DicMarginCategoryPageVO {


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "裕量计算分类编码")
    private String marginCategoryCode;

    @ApiModelProperty(value = "裕量描述")
    private String marginCategoryDesc;

    @ApiModelProperty(value = "工作裕量")
    private BigDecimal marginFactor;

    @ApiModelProperty(value = "工作裕量最小值")
    private BigDecimal marginFactorMin;

    @ApiModelProperty(value = "工作裕量最大值")
    private BigDecimal marginFactorMax;

    @ApiModelProperty(value = "应急裕量")
    private BigDecimal marginFactorEmergency;

    @ApiModelProperty(value = "应急裕量最小值")
    private BigDecimal marginFactorEmergencyMin;

    @ApiModelProperty(value = "应急裕量最大值")
    private BigDecimal marginFactorEmergencyMax;

    @ApiModelProperty(value = "辅助裕量")
    private BigDecimal marginFactorAssist;

    @ApiModelProperty(value = "辅助裕量最小值")
    private BigDecimal marginFactorAssistMin;

    @ApiModelProperty(value = "辅助裕量最大值")
    private BigDecimal marginFactorAssistMax;

    @ApiModelProperty(value = "圆整方向")
    private String roundingDirection;

    @ApiModelProperty(value = "圆整增量")
    private String roundingIncrement;

    @ApiModelProperty(value = "状态 1:启用；2:禁用")
    private Integer marginCategoryStatus;

    @ApiModelProperty(value = "删除标识")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;


}
