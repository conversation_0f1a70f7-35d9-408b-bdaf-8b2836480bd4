package com.inossem.wms.common.model.masterdata.storagebin.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 仓位数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "仓位数据传输对象", description = "仓位数据传输对象")
public class DicWhStorageBinDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/
    /** 仓库编码 */
    @ApiModelProperty(value = "仓库编码" , example = "S200")
    private String whCode;

    /** 仓库名称 */
    @ApiModelProperty(value = "仓库名称" , example = "英诺森仓库沈阳")
    private String whName;

    /** 存储类型编码 */
    @ApiModelProperty(value = "存储类型编码" , example = "801")
    private String typeCode;

    /** 存储类型名称 */
    @ApiModelProperty(value = "存储类型名称" , example = "入库临时区")
    private String typeName;

    /** 存储区编码 */
    @ApiModelProperty(value = "存储区编码" , example = "s008")
    private String sectionCode;

    /** 存储区名称 */
    @ApiModelProperty(value = "存储区名称" , example = "存储区008")
    private String sectionName;

    /** 创建人编码 */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /** 创建人名称 */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /** 修改人编码 */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /** 修改人名称 */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /**
     * 是否入库冻结
     */
    @ApiModelProperty(value = "是否入库冻结【1是，0否】" , example = "0")
    private String freezeInputI18n;

    /**
     * 是否出库冻结
     */
    @ApiModelProperty(value = "是否出库冻结【1是，0否】" , example = "0")
    private String freezeOutputI18n;

    /**
     * 空仓位标识描述
     */
    @ApiModelProperty(value = "空仓位标识描述" , example = "否")
    private String isEmptyI18n;

    @ApiModelProperty(value = "是否为核岛工具间【0：否；1：是】")
    private String isNuclearIslandToolRoomI18n;

    /* ********************** 扩展字段 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName,groupWhNo", targetAttrName = "whCode,whName,groupWhNo")
    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    private Long whId;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName,groupTypeNo", targetAttrName = "typeCode,typeName,groupTypeNo")
    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @ApiModelProperty(value = "仓位" , example = "00")
    private String binCode;

    @RlatAttr(rlatTableName = "dic_wh_storage_section", sourceAttrName = "sectionCode,sectionName,groupDepositType", targetAttrName = "sectionCode,sectionName,groupDepositType")
    @ApiModelProperty(value = "存储区" , example = "s008")
    private Long sectionId;

    @ApiModelProperty(value = "入库冻结标识。未冻结0， 盘点冻结1 ，手动冻结2  wcs冻结3" , example = "0")
    private Integer freezeInput;

    @ApiModelProperty(value = "出库冻结标识。未冻结0， 盘点冻结1 ，手动冻结2  wcs冻结3" , example = "0")
    private Integer freezeOutput;

    @ApiModelProperty(value = "冻结原因" , example = "0")
    private String freezeReason;

    @ApiModelProperty(value = "空仓位标识，0未使用，空，1已使用，不为空" , example = "0")
    private Integer isEmpty;

    @ApiModelProperty(value = "是否默认仓位【1是，0否】" , example = "0")
    private Integer isDefault;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "是否为核岛工具间【0：否；1：是】")
    private Integer isNuclearIslandToolRoom;
    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode", targetAttrName = "corpCode")
    private Long corpId;
    private String corpCode;
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode", targetAttrName = "ftyCode")
    private Long ftyId;
    private String ftyCode;
    // 集团架/位编号
    private String groupShelfNo;
    // 集团行号
    private String groupLineNo;
    // 集团列号
    private String groupColumnNo;
    // 集团仓位编号
    private String groupBinNo;
    private String groupWhNo;
    private String groupTypeNo;
    private String groupDepositType;

    @ApiModelProperty(value = "仓位承重（KG）")
    private BigDecimal weight;

    @ApiModelProperty(value = "是否超重")
    private String overWeight;
}
