package com.inossem.wms.common.mybatisplus;

import static com.baomidou.mybatisplus.core.enums.SqlKeyword.AND;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.BETWEEN;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.EQ;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.GE;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.GT;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.IN;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.IS_NOT_NULL;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.IS_NULL;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.LE;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.LIKE;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.LT;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.NE;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.NOT_BETWEEN;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.NOT_IN;
import static com.baomidou.mybatisplus.core.enums.SqlKeyword.NOT_LIKE;
import static java.util.stream.Collectors.joining;

import java.lang.annotation.Annotation;
import java.lang.ref.WeakReference;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;

import org.apache.ibatis.reflection.property.PropertyNamer;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper;
import com.baomidou.mybatisplus.core.conditions.ISqlSegment;
import com.baomidou.mybatisplus.core.conditions.SharedString;
import com.baomidou.mybatisplus.core.conditions.query.Query;
import com.baomidou.mybatisplus.core.conditions.segments.MergeSegments;
import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import com.baomidou.mybatisplus.core.enums.SqlLike;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.ArrayUtils;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.ClassUtils;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlUtils;
import com.baomidou.mybatisplus.core.toolkit.support.ColumnCache;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.core.toolkit.support.SerializedLambda;
import com.inossem.wms.common.util.UtilString;

/**
 * WmsLambdaQueryWrapper设计用于
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021/4/30
 */
public class WmsLambdaQueryWrapper<T> extends AbstractLambdaWrapper<T, WmsLambdaQueryWrapper<T>> implements Query<WmsLambdaQueryWrapper<T>, T, SFunction<T, ?>> {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Map<String, ColumnCache> columnMap = null;
    private boolean initColumnMap = false;

    /**
     * 查询字段
     */
    private SharedString sqlSelect = new SharedString();

    /**
     * 不建议直接 new 该实例，使用 Wrappers.lambdaQuery(entity)
     */
    public WmsLambdaQueryWrapper() {
        this((T) null);
    }

    /**
     * 不建议直接 new 该实例，使用 Wrappers.lambdaQuery(entity)
     */
    public WmsLambdaQueryWrapper(T entity) {
        super.setEntity(entity);
        super.initNeed();
    }

    /**
     * 不建议直接 new 该实例，使用 Wrappers.lambdaQuery(entity)
     */
    public WmsLambdaQueryWrapper(Class<T> entityClass) {
        super.setEntityClass(entityClass);
        super.initNeed();
    }

    /**
     * 不建议直接 new 该实例，使用 Wrappers.lambdaQuery(...)
     */
    WmsLambdaQueryWrapper(T entity, Class<T> entityClass, SharedString sqlSelect, AtomicInteger paramNameSeq,
                       Map<String, Object> paramNameValuePairs, MergeSegments mergeSegments,
                       SharedString lastSql, SharedString sqlComment, SharedString sqlFirst) {
        super.setEntity(entity);
        super.setEntityClass(entityClass);
        this.paramNameSeq = paramNameSeq;
        this.paramNameValuePairs = paramNameValuePairs;
        this.expression = mergeSegments;
        this.sqlSelect = sqlSelect;
        this.lastSql = lastSql;
        this.sqlComment = sqlComment;
        this.sqlFirst = sqlFirst;
    }

    /**
     * SELECT 部分 SQL 设置
     *
     * @param columns 查询字段
     */
    @SafeVarargs
    @Override
    public final WmsLambdaQueryWrapper<T> select(SFunction<T, ?>... columns) {
        if (ArrayUtils.isNotEmpty(columns)) {
            this.sqlSelect.setStringValue(columnsToString(false, columns));
        }
        return typedThis;
    }

    /**
     * 过滤查询的字段信息(主键除外!)
     * <p>例1: 只要 java 字段名以 "test" 开头的             -> select(i -&gt; i.getProperty().startsWith("test"))</p>
     * <p>例2: 只要 java 字段属性是 CharSequence 类型的     -> select(TableFieldInfo::isCharSequence)</p>
     * <p>例3: 只要 java 字段没有填充策略的                 -> select(i -&gt; i.getFieldFill() == FieldFill.DEFAULT)</p>
     * <p>例4: 要全部字段                                   -> select(i -&gt; true)</p>
     * <p>例5: 只要主键字段                                 -> select(i -&gt; false)</p>
     *
     * @param predicate 过滤方式
     * @return this
     */
    @Override
    public WmsLambdaQueryWrapper<T> select(Class<T> entityClass, Predicate<TableFieldInfo> predicate) {
        if (entityClass == null) {
            entityClass = getEntityClass();
        } else {
            setEntityClass(entityClass);
        }
        Assert.notNull(entityClass, "entityClass can not be null");
        this.sqlSelect.setStringValue(TableInfoHelper.getTableInfo(entityClass).chooseSelect(predicate));
        return typedThis;
    }

    @Override
    public String getSqlSelect() {
        return sqlSelect.getStringValue();
    }

    /**
     * 用于生成嵌套 sql
     * <p>故 sqlSelect 不向下传递</p>
     */
    @Override
    protected WmsLambdaQueryWrapper<T> instance() {
        return new WmsLambdaQueryWrapper<>(getEntity(), getEntityClass(), null, paramNameSeq, paramNameValuePairs,
                new MergeSegments(), SharedString.emptyString(), SharedString.emptyString(), SharedString.emptyString());
    }

    @Override
    public void clear() {
        super.clear();
        sqlSelect.toNull();
    }

    protected String columnToString(SFunction<T, ?> column, String tableAlias) {
        return columnToString(column, tableAlias,true);
    }

    protected String columnToString(SFunction<T, ?> column, String tableAlias, boolean onlyColumn) {
        return getColumn(resolve(column), tableAlias, onlyColumn);
    }

    /**
     * 获取 SerializedLambda 对应的列信息，从 lambda 表达式中推测实体类
     * <p>
     * 如果获取不到列信息，那么本次条件组装将会失败
     *
     * @param lambda     lambda 表达式
     * @param onlyColumn 如果是，结果: "name", 如果否： "name" as "name"
     * @return 列
     * @throws com.baomidou.mybatisplus.core.exceptions.MybatisPlusException 获取不到列信息时抛出异常
     * @see SerializedLambda#getImplClass()
     * @see SerializedLambda#getImplMethodName()
     */
    private String getColumn(SerializedLambda lambda, String tableAlias, boolean onlyColumn) {
        Class<?> aClass = getInstantiatedType(lambda);
        tryInitCache(aClass);
        String fieldName = PropertyNamer.methodToProperty(lambda.getImplMethodName());
        ColumnCache columnCache = getColumnCache(fieldName, aClass);
        return onlyColumn ? tableAlias + "." + columnCache.getColumn() : tableAlias + "." + columnCache.getColumnSelect();
    }

    private void tryInitCache(Class<?> lambdaClass) {
        if (!initColumnMap) {
            final Class<T> entityClass = getEntityClass();
            if (entityClass != null) {
                lambdaClass = entityClass;
            }
            columnMap = LambdaUtils.getColumnMap(lambdaClass);
            initColumnMap = true;
        }
        Assert.notNull(columnMap, "can not find lambda cache for this entity [%s]", lambdaClass.getName());
    }

    private ColumnCache getColumnCache(String fieldName, Class<?> lambdaClass) {
        ColumnCache columnCache = columnMap.get(LambdaUtils.formatKey(fieldName));
        Assert.notNull(columnCache, "can not find lambda cache for this property [%s] of entity [%s]",
                fieldName, lambdaClass.getName());
        return columnCache;
    }

    /**
     * 获取in表达式 包含括号
     *
     * @param value 集合
     */
    protected ISqlSegment inExpression(Collection<?> value) {
        return () -> value.stream().map(i -> formatSql("{0}", i)).collect(joining(StringPool.COMMA, StringPool.LEFT_BRACKET, StringPool.RIGHT_BRACKET));
    }

    private String getAliasTableName(Class<?> aliasTableClass) {
        Annotation annotation = aliasTableClass.getAnnotation(TableName.class);
        String tableName;
        if (annotation == null) {
            tableName = UtilString.getSnake(aliasTableClass.getSimpleName());
        } else {
            tableName = ((TableName) annotation).value();
        }
        return tableName;
    }

//    private String[] getAliasTableNames(Class<?>[] aliasTableClasses) {
//        List<String> tableNames = new ArrayList<>();
//        for (Class<?> aliasTableClass : aliasTableClasses) {
//            Annotation annotation = aliasTableClass.getAnnotation(TableName.class);
//            if (annotation == null) {
//                tableNames.add(UtilString.getSnake(aliasTableClass.getSimpleName()));
//            } else {
//                tableNames.add(((TableName) annotation).value());
//            }
//        }
//        return tableNames.toArray(new String[tableNames.size()]);
//    }

    public WmsLambdaQueryWrapper<T> eq(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), EQ, () -> formatSql("{0}", val));
    }

    public WmsLambdaQueryWrapper<T> ne(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), NE, () -> formatSql("{0}", val));
    }

    public WmsLambdaQueryWrapper<T> gt(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), GT, () -> formatSql("{0}", val));
    }

    public WmsLambdaQueryWrapper<T> ge(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), GE, () -> formatSql("{0}", val));
    }

    public WmsLambdaQueryWrapper<T> lt(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), LT, () -> formatSql("{0}", val));
    }

    public WmsLambdaQueryWrapper<T> le(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), LE, () -> formatSql("{0}", val));
    }

    public WmsLambdaQueryWrapper<T> like(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), LIKE, () -> formatSql("{0}", SqlUtils.concatLike(val, SqlLike.DEFAULT)));
    }

    public WmsLambdaQueryWrapper<T> notLike(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), NOT_LIKE, () -> formatSql("{0}", SqlUtils.concatLike(val, SqlLike.DEFAULT)));
    }

    public WmsLambdaQueryWrapper<T> likeLeft(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), LIKE, () -> formatSql("{0}", SqlUtils.concatLike(val, SqlLike.LEFT)));
    }

    public WmsLambdaQueryWrapper<T> likeRight(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), LIKE, () -> formatSql("{0}", SqlUtils.concatLike(val, SqlLike.RIGHT)));
    }

    public WmsLambdaQueryWrapper<T> between(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val1, Object val2) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), BETWEEN, () -> formatSql("{0}", val1), AND,
                () -> formatSql("{0}", val2));
    }

    public WmsLambdaQueryWrapper<T> notBetween(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Object val1, Object val2) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), NOT_BETWEEN, () -> formatSql("{0}", val1), AND,
                () -> formatSql("{0}", val2));
    }

    public WmsLambdaQueryWrapper<T> isNull(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), IS_NULL);
    }

    public WmsLambdaQueryWrapper<T> isNotNull(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), IS_NOT_NULL);
    }

    public WmsLambdaQueryWrapper<T> in(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Collection<?> coll) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), IN, inExpression(coll));
    }

    public WmsLambdaQueryWrapper<T> notIn(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, Collection<?> coll) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), NOT_IN, inExpression(coll));
    }

    public WmsLambdaQueryWrapper<T> inSql(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, String inValue) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), IN, () -> String.format("(%s)", inValue));
    }

    public WmsLambdaQueryWrapper<T> notInSql(boolean condition, SFunction<T, ?> column, Class<?> aliasTableClass, String inValue) {
        return doIt(condition, () -> columnToString(column, getAliasTableName(aliasTableClass)), NOT_IN, () -> String.format("(%s)", inValue));
    }

	public WmsLambdaQueryWrapper<T> groupBy(Class<?> aliasTableClass, SFunction<T, ?> column) {
		return doIt(true, SqlKeyword.GROUP_BY, () -> columnToString(column, getAliasTableName(aliasTableClass)));
	}

	public WmsLambdaQueryWrapper<T> orderByDesc(Class<?> aliasTableClass, SFunction<T, ?> column) {
		return doIt(true, SqlKeyword.ORDER_BY, () -> columnToString(column, getAliasTableName(aliasTableClass)), SqlKeyword.DESC);
	}

	@Override
	public WmsLambdaQueryWrapper<T> select(boolean condition, List<SFunction<T, ?>> columns) {
		// TODO Auto-generated method stub
		return null;
	}
	
	
    /**
     * SerializedLambda 反序列化缓存
     */
    private static final Map<String, WeakReference<SerializedLambda>> FUNC_CACHE = new ConcurrentHashMap<>();

    /**
     * 解析 lambda 表达式, 该方法只是调用了 {@link SerializedLambda#resolve(SFunction)} 中的方法，在此基础上加了缓存。
     * 该缓存可能会在任意不定的时间被清除
     *
     * @param func 需要解析的 lambda 对象
     * @param <T>  类型，被调用的 Function 对象的目标类型
     * @return 返回解析后的结果
     * @see SerializedLambda#resolve(SFunction)
     */
    public static <T> SerializedLambda resolve(SFunction<T, ?> func) {
        Class<?> clazz = func.getClass();
        String name = clazz.getName();
        return Optional.ofNullable(FUNC_CACHE.get(name))
                .map(WeakReference::get)
                .orElseGet(() -> {
                    SerializedLambda lambda = SerializedLambda.extract(func);
                    FUNC_CACHE.put(name, new WeakReference<>(lambda));
                    return lambda;
                });
    }

    public Class<?> getInstantiatedType(SerializedLambda sl) {
        String instantiatedTypeName = sl.getInstantiatedMethodType().substring(2, sl.getInstantiatedMethodType().indexOf(';'));
        instantiatedTypeName = instantiatedTypeName.replace('/', '.');
        return ClassUtils.toClassConfident(instantiatedTypeName);
    }
    
    
    protected final String formatSql(String sqlStr, Object... params) {
        return formatSqlMaybeWithParam(sqlStr, params);
    }
    
    protected WmsLambdaQueryWrapper<T> doIt(boolean condition, ISqlSegment... sqlSegments) {
    	return maybeDo(condition, () -> appendSqlSegments(sqlSegments));
    }
    
    
}
