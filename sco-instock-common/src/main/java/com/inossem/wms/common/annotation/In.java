package com.inossem.wms.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * BizContext 参数
 * <AUTHOR>
 * @date 2021/6/4
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface In {

    /**
     * BizContext 中包含的入参
     * 示例 BizContext.BIZ_CONTEXT_KEY_PO#BizReceiptDemandPlanHeadDTO
     * 多参数用逗号间隔
     */
    String[] parameter();

    /**
     * BizContext 中包含的必填参数
     * 示例 BizContext.BIZ_CONTEXT_KEY_PO#BizReceiptDemandPlanHeadDTO
     * 多参数用逗号间隔
     */
    String[] required() default {};


}
