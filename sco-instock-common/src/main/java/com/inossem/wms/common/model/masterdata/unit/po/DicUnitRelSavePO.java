package com.inossem.wms.common.model.masterdata.unit.po;

import java.io.Serializable;

import com.inossem.wms.common.model.masterdata.unit.dto.DicUnitRelDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "单位换算保存入参类", description = "单位换算保存入参类")
@Data
public class DicUnitRelSavePO implements Serializable {

    private static final long serialVersionUID = -6605471184135368592L;

    @ApiModelProperty(value = "单位换算实体类")
    private DicUnitRelDTO relUnitInfo;
}
