package com.inossem.wms.common.model.masterdata.mat.info.po;

import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> wang
 * @description 确认并拆分物料信息
 * @date 2022/3/30 12:18
 */
@ApiModel(value = "批次信息确认拆分", description = "批次信息确认拆分入参类")
@Data
public class DicMaterialListConfirmSplitPO implements Serializable {

    private static final long serialVersionUID = -1982739142087195315L;

    @ApiModelProperty(value = "批次信息实体类")
    private List<BizBatchInfoDTO> materialDTOList;
}
