package com.inossem.wms.common.model.masterdata.spec.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 分类表查询入参类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "分类表查询入参类", description = "分类表查询入参类")
public class BizSpecClassifySearchPO extends PageCommon {

    private static final long serialVersionUID = -6661152208172473789L;

    @ApiModelProperty(value = "分类代码" , example = "T1")
    private String specClassifyCode;

    @ApiModelProperty(value = "分类描述" , example = "分类1")
    private String specClassifyName;
}
