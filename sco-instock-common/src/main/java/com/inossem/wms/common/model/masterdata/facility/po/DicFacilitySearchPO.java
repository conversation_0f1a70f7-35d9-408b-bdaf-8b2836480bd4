package com.inossem.wms.common.model.masterdata.facility.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设施管理查询入参类
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicFacilitySearchPO", description = "设施管理查询入参类")
public class DicFacilitySearchPO extends PageCommon {

    @ApiModelProperty("设施编码")
    private String facilityCode;

    @ApiModelProperty("设施描述")
    private String facilityName;

    @ApiModelProperty("设施位置描述")
    private String locationDes;

    @ApiModelProperty("设施费用（USD/天）")
    private BigDecimal cost;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("预订日期")
    private Date targetDate;

    @ApiModelProperty("是否删除【1是，0否】")
    private Integer isDelete;

}
