package com.inossem.wms.common.rocketmq;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.base.Throwables;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.rocketmq.dto.MQDTO;
import com.inossem.wms.common.util.UtilSpring;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPullConsumer;
import org.apache.rocketmq.client.consumer.PullResult;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.MixAll;
import org.apache.rocketmq.common.Pair;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.rocketmq.common.protocol.body.Connection;
import org.apache.rocketmq.common.protocol.body.ConsumeMessageDirectlyResult;
import org.apache.rocketmq.common.protocol.body.ConsumerConnection;
import org.apache.rocketmq.tools.admin.MQAdminExt;
import org.apache.rocketmq.tools.admin.api.MessageTrack;
import org.slf4j.MDC;
import org.springframework.cglib.proxy.Enhancer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * rocket队列-生产者处理类
 *
 * <AUTHOR>
 */
@Slf4j
public class RocketMQProducerProcessor {

    private final DefaultMQProducer producer = UtilSpring.getBean("producer");
    private final DefaultMQProducer producerAck = UtilSpring.getBean("producerAck");
    private final MQAdminExt mqAdminExt = UtilSpring.getBean(MQAdminExt.class);

    public static RocketMQProducerProcessor getInstance() {
        Enhancer e = new Enhancer();
        e.setSuperclass(RocketMQProducerProcessor.class);
        e.setCallback(new ProducerProxyAck());
        return (RocketMQProducerProcessor)e.create();
    }

    /**
     * 同步发送消息
     *
     * @param message 发送消息
     * <AUTHOR>
     * @date 2021/2/24 16:21
     * @return void
     */
    public Object SyncMQSend(ProducerMessageContent message) {
        try {
            // 发送消息
            SendResult sendResult = producer.send(message, 1000*10);
            log.info("MQ同步发送消息，topic为：{}，tags为：{}，返回结果为：{}", message.getTopic(), message.getTags(), sendResult.getSendStatus());
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    /**
     * 同步发送消息
     *
     * @param message 发送消息
     * @return void
     * <AUTHOR>
     * @date 2021/2/24 16:21
     */
    public SendResult SyncMQSendWithResult(ProducerMessageContent message) {
        SendResult sendResult = null;
        try {
            // 发送消息
            sendResult = producer.send(message, 1000 * 10);
            log.info("MQ同步发送消息，topic为：{}，tags为：{}，返回结果为：{}", message.getTopic(), message.getTags(), sendResult.getSendStatus());
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
        }
        return sendResult;
    }

    /**
     * 异步发送消息
     *
     * @param message 发送消息
     * <AUTHOR>
     * @date 2021/2/24 16:37
     * @return void
     */
    public void AsyncMQSend(ProducerMessageContent message) {
        try {
            String requestId= MDC.get("requestId");
            // 发送失败重发次数
            producer.setRetryTimesWhenSendAsyncFailed(2);
            producer.send(message, new SendCallback() {

                @Override
                public void onSuccess(SendResult sendResult) {
                    MDC.put("requestId", requestId);
                    log.info("MQ异步发送消息，topic为：{}，tags为：{}，返回结果为：{}", message.getTopic(), message.getTags(), sendResult.getSendStatus());
                }

                @Override
                public void onException(Throwable e) {
                    MDC.put("requestId", requestId);
                    log.info("MQ异步发送消息失败，topic为：{}，tags为：{}", message.getTopic(), message.getTags());
                    log.error(ExceptionUtils.getStackTrace(e));
                }
            });
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FAILURE, e.getMessage());
        }
    }

    /**
     * 同步回调异步发送消息
     *
     * @param message 发送消息
     * <AUTHOR>
     * @date 2021/2/24 16:37
     * @return void
     */
    private void asyncMQSendAck(ProducerMessageContent message) {
        try {
            // 发送失败重发次数
            producerAck.setRetryTimesWhenSendAsyncFailed(2);
            producerAck.send(message, new SendCallback() {

                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("MQ回调异步发送消息，topic为：{}，tags为：{}，返回结果为：{}", message.getTopic(), message.getTags(), sendResult.getSendStatus());
                }

                @Override
                public void onException(Throwable e) {
                    log.info("MQ回调异步发送消息失败，topic为：{}，tags为：{}", message.getTopic(), message.getTags());
                    log.error(ExceptionUtils.getStackTrace(e));
                }
            });
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FAILURE, e.getMessage());
        }
    }

    /**
     * 单向发送消息 这种方式主要用在不特别关心发送结果的场景
     *
     * @param message
     * <AUTHOR>
     * @date 2021/2/24 16:44
     * @return void
     */
    public void oneWayMQSend(ProducerMessageContent message) throws Exception {
        // 单向发送消息
        producer.sendOneway(message);
    }

    /**
     * 消息重发
     *
     * @param consumerGroup
     * @param topic
     * @param msgId
     * <AUTHOR>
     * @date 2021/2/26 15:46
     * @return void
     */
    public void reSend(String consumerGroup, String topic, String msgId) throws Exception {
        ConsumeMessageDirectlyResult sendRuslt;
        try {
            ConsumerConnection consumerConnection = mqAdminExt.examineConsumerConnectionInfo(consumerGroup);
            for (Connection connection : consumerConnection.getConnectionSet()) {
                if (StringUtils.isBlank(connection.getClientId())) {
                    continue;
                }
                log.info("clientId={}", connection.getClientId());
                sendRuslt = mqAdminExt.consumeMessageDirectly(consumerGroup, connection.getClientId(), topic, msgId);
                sendRuslt.getConsumeResult();
            }
        } catch (Exception e) {
            throw Throwables.propagate(e);
        }
    }

    /**
     * 获取消息队列
     *
     * @param topic 主题
     * @param begin 开始时间
     * @param end 结束时间
     * <AUTHOR>
     * @date 2021/2/26 17:17
     * @return java.util.List<com.inossem.wms.common.model.rocketmq.MessageView>
     */
    public List<MQDTO> queryMessageByTopic(String topic, final long begin, final long end) {
        DefaultMQPullConsumer consumer = new DefaultMQPullConsumer(MixAll.TOOLS_CONSUMER_GROUP);
        List<MQDTO> MQDTOList = new ArrayList();
        topic = RocketMQconfigutationProperties.TOPIC;
        try {
            String subExpression = "*";
            consumer.start();
            Set<MessageQueue> mqs = consumer.fetchSubscribeMessageQueues(topic);
            for (MessageQueue mq : mqs) {
                long minOffset = consumer.searchOffset(mq, begin);
                long maxOffset = consumer.searchOffset(mq, end);

                READQ:
                for (long offset = minOffset; offset <= maxOffset;) {
                    try {
                        PullResult pullResult = consumer.pull(mq, subExpression, offset, 32);
                        offset = pullResult.getNextBeginOffset();
                        switch (pullResult.getPullStatus()) {
                            case FOUND:
                                List<MQDTO> MQDTOListByQuery = Lists.transform(pullResult.getMsgFoundList(), new Function<MessageExt, MQDTO>() {
                                    @Override
                                    public MQDTO apply(MessageExt messageExt) {
                                        messageExt.setBody(null);
                                        return MQDTO.fromMessageExt(messageExt);
                                    }
                                });

                                List<MQDTO> filteredList = Lists.newArrayList(Iterables.filter(MQDTOListByQuery, new Predicate<MQDTO>() {
                                    @Override
                                    public boolean apply(MQDTO messageView) {
                                        if (messageView.getStoreTimestamp() < begin || messageView.getStoreTimestamp() > end) {
                                            log.info("队列消息列表：begin={}，end={}；搜索时间不在{}-{}范围", begin, end, messageView.getStoreTimestamp(),
                                                new Date(messageView.getStoreTimestamp()).toString());
                                        }
                                        return messageView.getStoreTimestamp() >= begin && messageView.getStoreTimestamp() <= end;
                                    }
                                }));
                                MQDTOList.addAll(filteredList);
                                break;
                            case NO_MATCHED_MSG:
                            case NO_NEW_MSG:
                            case OFFSET_ILLEGAL:
                                break READQ;
                            default:
                                log.info("队列消息列表 pullStatus为空！");
                        }
                    } catch (Exception e) {
                        break;
                    }
                }
            }
            Collections.sort(MQDTOList, new Comparator<MQDTO>() {
                @Override
                public int compare(MQDTO o1, MQDTO o2) {
                    if (o1.getStoreTimestamp() - o2.getStoreTimestamp() == 0) {
                        return 0;
                    }
                    return (o1.getStoreTimestamp() > o2.getStoreTimestamp()) ? -1 : 1;
                }
            });
            return MQDTOList;
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            return null;
        } finally {
            consumer.shutdown();
        }
    }

    /**
     * 消息详情
     *
     * @param subject
     * @param msgId
     * <AUTHOR>
     * @date 2021/3/5 16:30
     * @return org.apache.rocketmq.common.Pair<com.inossem.wms.common.model.rocketmq.MessageView,java.util.List<org.apache.rocketmq.tools.admin.api.MessageTrack>>
     */
    public Pair<MQDTO, List<MessageTrack>> viewMessage(String subject, final String msgId) throws Exception {
        try {

            MessageExt messageExt = mqAdminExt.viewMessage(subject, msgId);
            List<MessageTrack> messageTrackList = mqAdminExt.messageTrackDetail(messageExt);
            return new Pair<>(MQDTO.fromMessageExt(messageExt), messageTrackList);
        } catch (Exception e) {
            throw new Exception(String.format("Failed to query message by Id: %s", msgId));
        }
    }
}
