/*
 * Copyright (c) 2020. Inossem All rights reserved.
 */

package com.inossem.wms.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 消息队列监听
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface WmsMQListener {

    /**
     * tags
     */
    String tags();

    /**
     * 回调方法
     */
    String rollback() default "";

}
