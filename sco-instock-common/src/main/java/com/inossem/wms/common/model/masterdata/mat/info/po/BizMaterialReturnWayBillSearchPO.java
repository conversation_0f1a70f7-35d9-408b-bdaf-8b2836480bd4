package com.inossem.wms.common.model.masterdata.mat.info.po;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/25 19:28
 */
@ApiModel(value = "物料保存入参类", description = "物料保存入参类")
@Data
public class BizMaterialReturnWayBillSearchPO extends PageCommon {

    @TableField("biz_receipt_register_head.receipt_code")
    @ApiModelProperty(value = "到货登记单号")
    private String registerCode;

    @ApiModelProperty(value = "物资编码")
    @TableField("dic_material.mat_code")
    private String childMatCode;

    @ApiModelProperty(value = "描述")
    private String childMatName;

    @ApiModelProperty(value = "物料编码")
    @TableField("p.mat_code")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "LOT包号（采购包）")
    private String extend2;

    @ApiModelProperty(value = "规格型号")
    private String extend24;

    @ApiModelProperty(value = "UP码")
    private String extend29;

    @ApiModelProperty(value = "领料退库单号")
    @TableField("biz_receipt_return_head.receipt_code")
    private String returnReceiptCode;
}
