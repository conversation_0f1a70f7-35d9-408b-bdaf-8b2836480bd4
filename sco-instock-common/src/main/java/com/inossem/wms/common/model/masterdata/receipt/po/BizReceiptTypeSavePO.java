package com.inossem.wms.common.model.masterdata.receipt.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/27 14:02
 */
@ApiModel(value = "单据类型对应存储区查询入参类", description = "单据类型对应存储区查询入参类")
@Data
public class BizReceiptTypeSavePO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据类型描述")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "仓库号id" )
    private Long whId;

    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    @ExcelIgnore
    private Long typeId;

    @ApiModelProperty(value = "存储区id", example = "1")
    private Long sectionId;

    @ApiModelProperty(value = "上架策略id")
    private Long loadId;

    @ApiModelProperty(value = "下架策略id")
    private Long unloadId;

}
