package com.inossem.wms.common.model.masterdata.cell.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 存储单元主数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "存储单元主数据传输对象", description = "存储单元主数据传输对象")
public class DicWhStorageCellHttpVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "唯一主键", type = IdType.ASSIGN_ID)
    private String node;

    @ApiModelProperty(value = "物品名称", example = "P001")
    private String name;

    @ApiModelProperty(value = "物品位置", example = "0")
    private String pos;

    @ApiModelProperty(value = "单件重量", example = "0")
    private BigDecimal weightUnit;
}
