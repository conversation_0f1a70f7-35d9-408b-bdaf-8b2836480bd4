package com.inossem.wms.common.model.masterdata.receipt.po;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/27 13:19
 */
@ApiModel(value = "单据类型对应存储区查询入参类", description = "单据类型对应存储区查询入参类")
@Data
public class BizReceiptSearchPO extends PageCommon {


    @ApiModelProperty(value = "仓库号id")
    private Long whId;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;
}
