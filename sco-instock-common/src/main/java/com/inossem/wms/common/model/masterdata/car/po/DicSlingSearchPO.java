package com.inossem.wms.common.model.masterdata.car.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 吊带管理分页列表查询入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="吊带管理分页列表查询入参", description="吊带管理分页列表查询入参")
public class DicSlingSearchPO extends PageCommon {

    @ApiModelProperty(value = "吊带编号")
    private String slingCode;

}
