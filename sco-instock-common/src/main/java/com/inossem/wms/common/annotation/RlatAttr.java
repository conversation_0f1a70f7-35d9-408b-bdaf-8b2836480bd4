package com.inossem.wms.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 关联属性填充注解 配置在VO类的自有属性中的外键字段上 可以根据配置信息填充关联属性的数据
 * 
 * <AUTHOR>
 * @date 2021/1/28 15:15
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RlatAttr {

    /**
     * 关联表表名 如果是关联确定表可以直接写表名，例如rlatTableName = "test_user" 如果是关联不确定表，比如单据行项目表存储了前序单据行项目id与前序单据类型两个字段
     * 需要根据前序单据类型才能确认前序单据行项目id是哪个表的，则需要写对应表达式 例如rlatTableName =
     * {"test_pre_receipt_item_1:preReceiptType=1001,1002","test_pre_receipt_item_2:preReceiptType=1002"} 支持多值对应一个表
     * 例如rlatTableName = {"test_pre_receipt_item_1:preReceiptType=1001,1002","test_pre_receipt_item_2:preReceiptType=1002"}
     * 该表达式的意思是当preReceiptType=1001时去test_pre_receipt_item_1表查询，当preReceiptType=1002时去test_pre_receipt_item_2表查询
     * 
     * @return
     */
    String[] rlatTableName();

    /**
     * 源属性名 多个用半角逗号分隔，需要与目标属性名成对设置
     *
     * 参数 为 * 的时候，全属性填充
     * 
     * @return
     */
    String sourceAttrName();

    /**
     * 目标属性名 多个用半角逗号分隔，需要与源属性名成对设置
     * 
     * @return
     */
    String targetAttrName();
}
