package com.inossem.wms.common.model.masterdata.exchangerate.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 汇率主数据查询入参类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@ApiModel(value = "汇率主数据查询入参类", description = "汇率主数据查询入参类")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DicExchangeRateSearchPO extends PageCommon {

    @ApiModelProperty(value = "年")
    private Integer year;

    @ApiModelProperty(value = "月")
    private Integer month;
}
