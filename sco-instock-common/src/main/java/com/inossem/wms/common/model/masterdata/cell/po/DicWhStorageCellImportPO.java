package com.inossem.wms.common.model.masterdata.cell.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 存储单元导入PO
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "存储单元导入PO", description = "存储单元导入PO")
public class DicWhStorageCellImportPO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "存储单元编码" , example = "7")
    @ExcelProperty(value = "电子秤ID", index =0)
    private String cellCode;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @ExcelProperty(value = "物料编码", index =1)
    private String matCode;

    @ApiModelProperty(value = "仓库号" , example = "M001005")
    @ExcelProperty(value = "仓库号", index =2)
    private String whCode;

    @ApiModelProperty(value = "存储类型" , example = "M001005")
    @ExcelProperty(value = "存储类型", index =3)
    private String typeCode;

    @ApiModelProperty(value = "仓位编码" , example = "M001005")
    @ExcelProperty(value = "仓位编码", index =4)
    private String binCode;

    @ApiModelProperty(value = "托盘类型类型" , example = "0")
    @ExcelProperty(value = "托盘类型类型", index =5)
    private Integer cellType;

    @ApiModelProperty(value = "物料主键" , example = "7")
    @ExcelIgnore
    private Long matId;

    @ApiModelProperty(value = "仓位主键" , example = "7")
    @ExcelIgnore
    private Long binId;

}
