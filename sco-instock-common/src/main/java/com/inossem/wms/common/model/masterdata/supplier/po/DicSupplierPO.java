package com.inossem.wms.common.model.masterdata.supplier.po;

import java.util.List;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 供应商主数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicSupplier对象", description = "供应商主数据")
public class DicSupplierPO extends PageCommon {

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "是否内贸代理商【1是0否】")
    private Integer isInlandAgent;

    @ApiModelProperty(value = "停用,启用,待审批,已拒绝")
    private Integer receiptStatus;

    private List<Integer> receiptStatusList;

}
