package com.inossem.wms.common.model.bizdomain.threedimensional.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 温湿度
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "温湿度", description = "温湿度 查询出参")
public class ThreeDimensionalHumitureDataVO {

    @ApiModelProperty(value = "库房编码" , example = "A01")
    String storageType;//库房编码 A01

    @ApiModelProperty(value = "温度" , example = "33")
    Double temperature;//温度

    @ApiModelProperty(value = "湿度" , example = "44")
    Double humidity; //湿度
}
