package com.inossem.wms.common.model.masterdata.mat.info.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 物资返运单抬头表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizMaterialReturnHead对象", description="物资返运单抬头表")
@TableName("biz_material_return_head")
public class BizMaterialReturnHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1.删除0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "返运原因(退换货0  返修1 供货超量2 其他3)")
    private int returnReason;

    @ApiModelProperty(value = "返运类型,0 入库物项返运,1 库存物项返运")
    private Integer returnType;

    @ApiModelProperty(value = "其他原因")
    private String otherReason;

    @ApiModelProperty(value = "返运描述")
    private String returnRemark;

    @ApiModelProperty(value = "物资包装【1-委托业主负责，2-申请人负责，3-供应商负责】")
    private Integer packaging;

    @ApiModelProperty(value = "包装运输要求【1-无特殊要求，2-有特殊要求以附件形式详细说明】")
    private Integer transportRequire;

    @ApiModelProperty(value = "物资运输【1-委托业主负责，2-申请人负责，3-供应商负责】")
    private Integer transportWay;

    @ApiModelProperty(value = "物资通关【1-委托业主负责，2-申请人负责，3-供应商负责】")
    private Integer clearance;

    @ApiModelProperty(value = "附件信息【1-物资通关申请，2-包装、运输特殊要求，3-其它】")
    private Integer attachInfo;

    @ApiModelProperty(value = "收货人名称")
    private String consigneeName;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "收货人地址")
    private String consignessAddr;

    @ApiModelProperty(value = "邮编")
    private String postal;

    @ApiModelProperty(value = "联系人")
    private String contacts;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "其它返运信息备注")
    private String otherRemark;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "采购负责人id")
    private Long purchaseUserId;

}
