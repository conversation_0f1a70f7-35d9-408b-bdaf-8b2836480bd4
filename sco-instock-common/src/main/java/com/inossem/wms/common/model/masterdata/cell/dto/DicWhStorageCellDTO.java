package com.inossem.wms.common.model.masterdata.cell.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 存储单元主数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "存储单元主数据传输对象", description = "存储单元主数据传输对象")
public class DicWhStorageCellDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/

    @ApiModelProperty(value = "物料编码", example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料名称", example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "仓位", name = "binCode", example = "123456", required = true)
    private String binCode;

    @ApiModelProperty(value = "电子秤ID")
    private String electronicScaleId;

    @ApiModelProperty(value = "填充属性 - 物料净重" , example = "50")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "填充属性 - 重量容差" , example = "正负5%")
    private BigDecimal weightTolerance;

    @ApiModelProperty(value = "库存重量" , example = "1500")
    private BigDecimal weight;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "托盘编号", example = "P001")
    private String cellCode;

    @ApiModelProperty(value = "托盘类型 0 轻型 1 重型 2 电子秤", example = "0")
    private Integer cellType;

    @ApiModelProperty(value = "是否冻结【1是，0否】", example = "0")
    private Integer isFreeze;

    @ApiModelProperty(value = "是否为空【1是，0否】", example = "0")
    private Integer isEmpty;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,netWeight,weightTolerance", targetAttrName = "matCode,matName,netWeight,weightTolerance")
    @ApiModelProperty(value = "物料id", example = "1", required = false)
    private Long matId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    @ApiModelProperty(value = "仓位id", example = "1", required = false)
    private Long binId;

    @ApiModelProperty(value = "设备状态", example = "0")
    private String electricStatus;

    @ApiModelProperty(value = "设备电量", example = "P001")
    private String electricQuantity;

    @ApiModelProperty(value = "唤醒阀值（g）", example = "P001")
    private String wakeUpThreshold;

}
