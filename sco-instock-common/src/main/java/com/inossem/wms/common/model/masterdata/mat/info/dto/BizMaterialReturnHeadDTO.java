package com.inossem.wms.common.model.masterdata.mat.info.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/25 19:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "物资返运数据对象传输", description = "物资返运数据对象传输")
public class BizMaterialReturnHeadDTO implements Serializable {

    /* =========================== 扩展字段开始 ===========================*/



    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码" , example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称" , example = "管理员")
    private String purchaseUserName;

    @SonAttr(sonTbName = "biz_material_return_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 物资返运行项目")
    private List<BizMaterialReturnItemDTO> itemDTOList;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称" , example = "入库单")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称" , example = "草稿")
    private String receiptStatusI18n;

    /* =========================== 扩展字段结束 ===========================*/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1.删除0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "填充属性 - 单据附件")
    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "返运原因")
    private int returnReason;

    @ApiModelProperty(value = "返运原因")
    private String returnReasonI18n;

    @ApiModelProperty(value = "返运类型")
    private Integer returnType;

    @ApiModelProperty(value = "返运类型")
    private String returnTypeI18n;

    @ApiModelProperty(value = "其他原因")
    private String otherReason;

    @ApiModelProperty(value = "返运描述")
    private String returnRemark;

    @ApiModelProperty(value = "采购订单号")
    private String ReferReceiptCode;

    @ApiModelProperty(value = "填充属性 - 审批人列表")
    @SonAttr(sonTbName = "biz_material_return_user", sonTbFkAttrName = "headId")
    private List<BizMaterialReturnUserDTO> inspectUserList;

    @ApiModelProperty(value = "审批单位名称- 移交人")
    private String companyHandOverUser;

    @ApiModelProperty(value = "审批人- 移交人")
    private String userNameHandOverUser;

    @ApiModelProperty(value = "审批创建时间- 移交人")
    private String createTimeStrHandOverUser;

    @ApiModelProperty(value = "审批单位名称- 接收人")
    private String companyRecipient;

    @ApiModelProperty(value = "审批人- 接收人")
    private String userNameRecipient;

    @ApiModelProperty(value = "审批创建时间- 接收人")
    private String createTimeStrRecipient;

    @ApiModelProperty(value = "审批单位名称- 参与人")
    private String companyParticipantsIn;

    @ApiModelProperty(value = "审批人- 参与人")
    private String userNameParticipantsIn;

    @ApiModelProperty(value = "审批创建时间- 参与人")
    private String createTimeStrParticipantsIn;


    @ApiModelProperty(value = "返运原因- 退换货")
    private String isReturn;

    @ApiModelProperty(value = "返运原因 - 返修 ")
    private String isRepair;
    
    @ApiModelProperty(value = "返运原因 - 供货超量  ")
    private String isExcessSupply;


    @ApiModelProperty(value = "物资包装【1-委托业主负责，2-申请人负责，3-供应商负责】")
    private Integer packaging;

    @ApiModelProperty(value = "包装运输要求【1-无特殊要求，2-有特殊要求以附件形式详细说明】")
    private Integer transportRequire;

    @ApiModelProperty(value = "物资运输【1-委托业主负责，2-申请人负责，3-供应商负责】")
    private Integer transportWay;

    @ApiModelProperty(value = "物资通关【1-委托业主负责，2-申请人负责，3-供应商负责】")
    private Integer clearance;

    @ApiModelProperty(value = "附件信息【1-物资通关申请，2-包装、运输特殊要求，3-其它】")
    private Integer attachInfo;

    @ApiModelProperty(value = "收货人名称")
    private String consigneeName;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "收货人地址")
    private String consignessAddr;

    @ApiModelProperty(value = "邮编")
    private String postal;

    @ApiModelProperty(value = "联系人")
    private String contacts;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "其它返运信息备注")
    private String otherRemark;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "仓储承包商")
    private String wareContractDept ;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "采购负责人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "purchaseUserCode,purchaseUserName")
    private Long purchaseUserId;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;


    // 物资返运手签
    private String sign1;
    private String signCompany1;
    private Date signDate1;
    private String sign2;
    private String signCompany2;
    private Date signDate2;
    private String sign3;
    private String signCompany3;
    private Date signDate3;
    private String sign4;
    private String signCompany4;
    private Date signDate4;
    private String sign5;
    private String signCompany5;
    private Date signDate5;
    private String sign6;
    private String signCompany6;
    private Date signDate6;
    private String sign7;
    private String signCompany7;
    private Date signDate7;
}
