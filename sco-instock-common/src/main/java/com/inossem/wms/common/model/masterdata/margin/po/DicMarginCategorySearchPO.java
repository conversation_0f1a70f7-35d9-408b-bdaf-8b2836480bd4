package com.inossem.wms.common.model.masterdata.margin.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
/**
 * 裕量分类
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@TableName("dic_margin_category")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="裕量分类查询")
public class DicMarginCategorySearchPO extends PageCommon {

    @ApiModelProperty(value = "裕量计算分类编码")
    private String marginCategoryCode;
}
