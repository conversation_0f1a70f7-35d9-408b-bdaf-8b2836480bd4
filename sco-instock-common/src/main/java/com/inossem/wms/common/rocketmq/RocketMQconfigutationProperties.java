package com.inossem.wms.common.rocketmq;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * rocketmq 配置类
 */
@Component
public class RocketMQconfigutationProperties {

    @Value("${rocket.namesrv-addr}")
    private String nameServerConf;
    @Value("${rocket.producer-group}")
    private String producerGroupNameConf;
    @Value("${rocket.consumer-group}")
    private String consumerGroupNameConf;
    @Value("${rocket.consumer-group-ack}")
    private String consumerGroupAckNameConf;
    @Value("${rocket.topic}")
    private String topicConf;

    /**
     * 注册服务
     */
    public static String NAMESERVER;
    /**
     * 生产组
     */
    public static String PRODUCERGROUPNAME;
    /**
     * 消费组
     */
    public static String  CONSUMERGROUPNAME;
    /**
     * 消费回调组
     */
    public static String CONSUMERGROUPACKNAME;
    /**
     * 订阅主题
     */
    public static String TOPIC;
    /**
     *  回调标识
     */
    public static String ACKMARK;
    /**
     *  消息回调tag
     */
    public static String ACKTAG;
    /**
     * 调用方法名
     */
    public static String METHODNAME;
    /**
     * 调用方法名
     */
    public static String SYNCMETHODNAME;


    @PostConstruct
    public void init() {
        NAMESERVER = nameServerConf;
        PRODUCERGROUPNAME = producerGroupNameConf;
        CONSUMERGROUPNAME = consumerGroupNameConf;
        CONSUMERGROUPACKNAME = consumerGroupAckNameConf;
        TOPIC = topicConf;
        ACKMARK = "AckMark";
        ACKTAG = "tag-ack";
        METHODNAME = "SyncMQSend";
        SYNCMETHODNAME = "SyncMQSendWithResult";
    }
}
