package com.inossem.wms.common.exception;

import com.inossem.wms.common.enums.EnumReturnMessageLevel;
import com.inossem.wms.common.enums.EnumReturnMsg;

import lombok.Getter;

/**
 * WMS系统基础异常类自带一个异常码errorCode
 * 
 * <AUTHOR>
 */
@Getter
public class WmsException extends RuntimeException {

    private static final long serialVersionUID = -3549258026559432905L;

    /**
     * 异常编码
     */
    protected EnumReturnMsg errorCode;
    /**
     * 参数
     */
    protected String[] args;
    /**
     * 异常级别
     */
    protected EnumReturnMessageLevel errorLevel;

    public WmsException(EnumReturnMsg enumReturnMsg, String... args) {
        super(enumReturnMsg.name());
        this.args = args;
        errorCode = enumReturnMsg;
        errorLevel = EnumReturnMessageLevel.ERROR;
    }

    public WmsException(String... args) {
        super(EnumReturnMsg.INIT_EXCEPTION_DES.name());
        this.args = args;
        errorCode = EnumReturnMsg.INIT_EXCEPTION_DES;
        errorLevel = EnumReturnMessageLevel.ERROR;
    }

    public WmsException(EnumReturnMessageLevel level, EnumReturnMsg enumReturnMsg, String... args) {
        super(enumReturnMsg.name());
        this.args = args;
        errorCode = enumReturnMsg;
        errorLevel = level;
    }
}
