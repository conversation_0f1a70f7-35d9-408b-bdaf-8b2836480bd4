package com.inossem.wms.common.model.masterdata.spec.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 特性分类与特性关系表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SpecRelClassifyFeature对象", description = "特性分类与特性关系表")
@TableName("biz_spec_classify_feature_rel")
public class BizSpecClassifyFeatureRel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id" , example = "1")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "特性分类ID" , example = "145874414010369")
    private Long classifyId;

    @ApiModelProperty(value = "特性ID" , example = "145874414010369")
    private Long featureId;

    @ApiModelProperty(value = "删除标识" , example = "0")
	@TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人" , example = "1")
    private Long createUserId;

    @ApiModelProperty(value = "修改人" , example = "1")
    private Long modifyUserId;

}
