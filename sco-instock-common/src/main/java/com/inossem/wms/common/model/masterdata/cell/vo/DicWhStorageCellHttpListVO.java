package com.inossem.wms.common.model.masterdata.cell.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.model.masterdata.cell.dto.DicWhStorageCellDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 存储单元主数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "存储单元主数据传输对象", description = "存储单元主数据传输对象")
public class DicWhStorageCellHttpListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<DicWhStorageCellHttpVO> data;
}
