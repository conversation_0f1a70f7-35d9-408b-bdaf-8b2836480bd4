package com.inossem.wms.common.rocketmq;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ObjectInputStream;
import java.lang.reflect.Method;
import java.util.List;

import org.apache.rocketmq.client.consumer.DefaultLitePullConsumer;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;

import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilSpring;

/**
 * 生产端动态代理类
 *
 * <AUTHOR>
 */
public class ProducerProxyAck implements MethodInterceptor {

    private final DefaultLitePullConsumer pullConsumer = UtilSpring.getBean("consumerAck");

    @Override
    public Object intercept(Object o, Method method, Object[] objects, MethodProxy methodProxy) throws Throwable {
        Object result = null;
        String methodName = method.getName();
        // 【同步方法发送】同步接收回调消息
        if (RocketMQconfigutationProperties.METHODNAME.equals(methodName)) {
            ProducerMessageContent messageContent = (ProducerMessageContent)objects[0];
            // 同步发送
            messageContent.setFlag(1);
            methodProxy.invokeSuper(o, objects);
            System.out.println("生产端，同步发送时间：" + System.currentTimeMillis());
            result = this.call(messageContent.getUserProperty(RocketMQconfigutationProperties.ACKTAG));
        } else if (RocketMQconfigutationProperties.SYNCMETHODNAME.equals(methodName)) {
            ProducerMessageContent messageContent = (ProducerMessageContent) objects[0];
            // 同步发送
            messageContent.setFlag(1);
            result = methodProxy.invokeSuper(o, objects);
            System.out.println("生产端，同步发送时间：" + System.currentTimeMillis());
            this.call(messageContent.getUserProperty(RocketMQconfigutationProperties.ACKTAG));
        } else {
            methodProxy.invokeSuper(o, objects);
        }
        return result;
    }

    private Object call(String tags) throws Exception {
        System.out.println("生产端，接收回调tags：" + tags);
        List<MessageExt> msgs;
        // 接收消息体
        byte[] msgBody = null;
        pollMsg:
        while (null == msgBody) {
            msgs = pullConsumer.poll(10000);
            if (UtilCollection.isEmpty(msgs)) {
                continue;
            }
            for (MessageExt msg : msgs) {
                if (tags.equals(msg.getTags())) {
                    msgBody = msg.getBody();
                    pullConsumer.commitSync();
                    break pollMsg;
                }
            }
            System.out.println("生产端，接收等待回调结果...：" + System.currentTimeMillis());
        }
        // 反序列化
        try(ObjectInputStream ois = new ObjectInputStream(new BufferedInputStream(new ByteArrayInputStream(msgBody)))){
            return ois.readObject();
        }
    }
}
