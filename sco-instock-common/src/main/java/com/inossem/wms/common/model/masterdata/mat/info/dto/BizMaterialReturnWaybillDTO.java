package com.inossem.wms.common.model.masterdata.mat.info.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizMaterialReturnWaybill对象", description="物资返运运单信息")
public class BizMaterialReturnWaybillDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "item表id")
    private Long itemId;

    @RlatAttr(rlatTableName = "biz_receipt_waybill", sourceAttrName = "*", targetAttrName = "waybillDTO")
    @ApiModelProperty(value = "运单id")
    private Long billId;

    @ApiModelProperty(value = "运单")
    private BizReceiptWaybillDTO waybillDTO;

    @ApiModelProperty(value = "序号" , example = "1")
    private String bid;

    @ApiModelProperty(value = "备注" , example = "备注")
    private String billRemark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "到货登记单号")
    private String registerCode;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料名称")
    private String matName;

    @ApiModelProperty(value = "运返箱号")
    private String returnBox;

    @ApiModelProperty(value = "返运数量")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "状态说明")
    private String statusDesc;

    @ApiModelProperty(value = "采购订单head表id" , example = "111")
    private Long purchaseReceiptHeadId;

    @ApiModelProperty(value = "采购订单item表id" , example = "111")
    private Long purchaseReceiptItemId;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行项目序号")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "扩展属性 - 批次图片")
    @SonAttr(sonTbName = "biz_batch_img", sonTbFkAttrName = "receiptItemId")
    private List<BizBatchImgDTO> bizBatchImgDTOList;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "领料退库单号")
    private String returnReceiptCode;
}
