package com.inossem.wms.common.model.masterdata.cell.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 存储单元主数据分页出参对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "存储单元主数据分页出参对象", description = "存储单元主数据分页出参对象")
public class DicWhStorageCellPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料名称" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "仓位", name = "binCode", example = "123456", required = true)
    private String binCode;

    @ApiModelProperty(value = "创建人Code" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "创建人描述", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "托盘类型描述" , example = "普通")
    private String cellTypeI18n;

    @ApiModelProperty(value = "冻结描述" , example = "已冻结")
    private String isFreezeI18n;

    @ApiModelProperty(value = "空托描述" , example = "空")
    private String isEmptyI18n;


    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "托盘编号" , example = "P001")
    private String cellCode;

    @ApiModelProperty(value = "托盘类型 0 轻型 1 重型" , example = "0")
    private Integer cellType;

    @ApiModelProperty(value = "是否冻结【1是，0否】" , example = "0")
    private Integer isFreeze;

    @ApiModelProperty(value = "是否为空【1是，0否】" , example = "0")
    private Integer isEmpty;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id" , example = "1", required = false)
    private Long matId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    @ApiModelProperty(value = "仓位id" , example = "1", required = false)
    private Long binId;
}
