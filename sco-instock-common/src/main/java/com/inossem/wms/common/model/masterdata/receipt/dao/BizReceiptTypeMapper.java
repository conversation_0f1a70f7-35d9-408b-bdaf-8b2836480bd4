package com.inossem.wms.common.model.masterdata.receipt.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.masterdata.receipt.entity.BizReceiptType;
import com.inossem.wms.common.model.masterdata.receipt.po.BizReceiptSearchPO;
import com.inossem.wms.common.model.masterdata.receipt.vo.BizReceiptPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 单据类型对应存储区表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
public interface BizReceiptTypeMapper extends WmsBaseMapper<BizReceiptType> {

    /**
     * 获取单据类型对应存储区列表-分页
     * @param page
     * @param wrapper
     * @return
     */
    List<BizReceiptPageVO> selectDicReceiptTypePageVOList(IPage<BizReceiptPageVO> page, @Param(Constants.WRAPPER) QueryWrapper<BizReceiptSearchPO> wrapper);
}
