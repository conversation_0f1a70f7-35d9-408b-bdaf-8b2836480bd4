package com.inossem.wms.common.model.masterdata.mat.info.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料主数据数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "物料主数据数据传输对象", description = "物料主数据数据传输对象")
public class DicMaterialImport implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码" , example = "7")
    @ExcelProperty(value = "单位编码", index =2)
    private String unitCode;

    /**
     * 物料组编码
     */
    @ApiModelProperty(value = "物料组编码" , example = "g1")
    @ExcelProperty(value = "物料组编码", index =3)
    private String matGroupCode;

    /**
     * 物料类型编码
     */
    @ApiModelProperty(value = "物料类型编码" , example = "A1")
    @ExcelProperty(value = "物料类型编码", index =4)
    private String matTypeCode;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private Long id;

    @ApiModelProperty(value = "成套主物料id")
    private Long parentMatId;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @ExcelProperty(value = "物料编码", index =0)
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    @ExcelProperty(value = "物料描述", index =1)
    private String matName;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    @ApiModelProperty(value = "计量单位id" , example = "7")
    @ExcelIgnore
    private Long unitId;

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组id" , example = "1")
    @ExcelIgnore
    private Long matGroupId;

    @RlatAttr(rlatTableName = "dic_material_type", sourceAttrName = "matTypeCode,matTypeName", targetAttrName = "matTypeCode,matTypeName")
    @ApiModelProperty(value = "物料类型id", example = "1")
    @ExcelIgnore
    private Long matTypeId;

    @ApiModelProperty(value = "长度" , example = "100")
    @ExcelProperty(value = "长度", index =5)
    private BigDecimal length;

    @ApiModelProperty(value = "宽度" , example = "100")
    @ExcelProperty(value = "宽度", index =6)
    private BigDecimal width;

    @ApiModelProperty(value = "高度" , example = "100")
    @ExcelProperty(value = "高度", index =7)
    private BigDecimal height;

    @ApiModelProperty(value = "长度/宽度/高度的单位" , example = "M")
    @ExcelProperty(value = "长度/宽度/高度的单位", index =8)
    private String unitLength;

    @ApiModelProperty(value = "毛重" , example = "100")
    @ExcelProperty(value = "毛重（kg）", index =9)
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "净重" , example = "100")
    @ExcelProperty(value = "净重", index =10)
    private BigDecimal netWeight;

    @ApiModelProperty(value = "重量的单位" , example = "KG")
    @ExcelProperty(value = "重量的单位", index =11)
    private String unitWeight;

    @ApiModelProperty(value = "体积" , example = "1000000")
    @ExcelProperty(value = "体积", index =12)
    private BigDecimal volume;

    @ApiModelProperty(value = "体积的单位" , example = "M3")
    @ExcelProperty(value = "体积的单位", index =13)
    private String unitVolume;

    @ApiModelProperty(value = "总货架寿命")
    @ExcelProperty(value = "总货架寿命", index = 14)
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "最小货架寿命")
    @ExcelProperty(value = "最小货架寿命", index = 15)
    private Integer shelfLifeMin;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    @ExcelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）", index = 16)
    private Integer packageType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    @ExcelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）", index = 17)
    private Integer depositType;

    @ApiModelProperty(value = "保质期" , example = "100")
    @ExcelProperty(value = "保质期", index =18)
    private Integer shelfLife;

    @ApiModelProperty(value = "保质期的单位 D-天 M-月" , example = "M")
    @ExcelProperty(value = "保质期的单位 D-天 M-月", index =19)
    private String unitShelfLife;

    @ApiModelProperty(value = "是否启用保质期【1是，0否】" , example = "1")
    @ExcelProperty(value = "是否启用保质期【1是，0否】", index =20)
    private Integer isShelfLife;

    @ApiModelProperty(value = "是否冻结【1是，0否】" , example = "0")
    @ExcelProperty(value = "是否冻结【1是，0否】", index =21)
    private Integer isFreeze;

    @ApiModelProperty(value = "是否危险物料【1是，0否】" , example = "0")
    @ExcelProperty(value = "是否危险物料【1是，0否】", index =22)
    private Integer isDangerous;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    @ExcelIgnore
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    @ExcelIgnore
    private Long modifyUserId;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "重量容差" , example = "正负5%")
    private BigDecimal weightTolerance;
}
