package com.inossem.wms.common.constant.workflow;

import java.awt.*;

/**
 * 审批流常量
 * 
 * <AUTHOR>
 * @date 2020/8/4 10:10
 */
public class WorkflowConst {
    /**
     * 动态流程图颜色定义
     **/
    public static final Color COLOR_NORMAL = new Color(0, 205, 0);
    public static final Color COLOR_CURRENT = new Color(255, 0, 0);

    /**
     * 定义生成流程图时的边距(像素)
     **/
    public static final int PROCESS_PADDING = 5;

    /**
     * 字体
     */
    public static final String FONT = "simhei";

    public static final String BPMN = "bpmn";

    public static final String ZIP = "zip";

    public static final String BAR = "bar";
}
