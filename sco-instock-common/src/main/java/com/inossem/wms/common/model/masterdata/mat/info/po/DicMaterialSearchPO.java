package com.inossem.wms.common.model.masterdata.mat.info.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ApiModel(value = "物料查询入参类", description = "物料查询入参类")
@Data
public class DicMaterialSearchPO extends PageCommon {

    @ApiModelProperty(value = "指令状态 201执行中 202异常 203完成 204取消 205待执行 206待触发" , example = "201")
    private Integer receiptStatus;


    @ApiModelProperty(value = "单据类型" , example = "414")
    private Integer receiptType;

    @ApiModelProperty(value = "领料出库单号" , example = "CK00000428")
    private String receiptCode;

    @ApiModelProperty(value = "物料主键" , example = "M001005")
    private Long matId;

    @ApiModelProperty(value = "物料编码" , example = "1005")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "物料类型" , example = "1")
    private Long matTypeId;

    @ApiModelProperty(value = "物料组" , example = "1")
    private Long matGroupId;

    @ApiModelProperty(value = "工厂主键" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "是否冻结【1是，0否】" , example = "0")
    private Integer isFreeze;

    @ApiModelProperty(value = "仓库主键" , example = "1")
    private Long whId;

    @ApiModelProperty(value = "净重" , example = "100")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "重量容差" , example = "正负5%")
    private BigDecimal weightTolerance;

    @ApiModelProperty(value = "变化重量")
    private BigDecimal variableWeight;

    @ApiModelProperty(value = "提交数量")
    private BigDecimal submitQty;
    private Integer unitizedFlag;
}
