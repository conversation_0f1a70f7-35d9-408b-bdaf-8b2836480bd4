package com.inossem.wms.common.model.masterdata.supplier.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 供应商主数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "DicSupplier对象", description = "供应商主数据")
public class DicSupplierExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    @ExcelProperty(value = "供应商类型")
    private String supplierTypeI18n;

    @ExcelProperty(value = "供应商分类")
    private String supplierClassI18n;

    @ExcelProperty(value = "供应商评定")
    private String supplierLevel;

    @ExcelProperty(value = "供应商属性")
    private String supplierAttrI18n;


    // 物料等级
    @ExcelProperty(value = "物料等级")
    private String matLevel;

    // 法人代表
    @ExcelProperty(value = "法人代表")
    private String legalPerson;

    @ExcelProperty(value = "联系人")
    private String contactPerson;

    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    @ExcelProperty(value = "注册资金（万元）")
    private BigDecimal registeredCapital;

    @ExcelProperty(value = "成立时间")
    private Date establishTime;

    @ExcelProperty(value = "纳税人识别码")
    private String creditCode;

    @ExcelProperty(value = "引入时间")
    private Date introductionTime;

    @ExcelProperty(value = "业务范围")
    private String scopeI18n;

    @ExcelProperty(value = "范围备注")
    private String remark;

    @ExcelProperty(value = "国家")
    private String country;

    @ExcelProperty(value = "省份")
    private String area;

    @ExcelProperty(value = "详细地址")
    private String address;

    @ExcelProperty(value = "有效开始时间")
    private Date startTime;

    @ExcelProperty(value = "有效截止时间")
    private Date endTime;

//    @ExcelProperty(value = "手机号")
//    private String phoneNumber;

    @ExcelProperty(value = "企业邮箱")
    private String email;

}
