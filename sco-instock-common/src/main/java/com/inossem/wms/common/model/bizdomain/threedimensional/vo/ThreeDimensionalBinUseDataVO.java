package com.inossem.wms.common.model.bizdomain.threedimensional.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 库房仓位利用率 是否为空
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库房仓位利用率", description = "库房仓位利用率 查询出参")
public class ThreeDimensionalBinUseDataVO {

    @ApiModelProperty(value = "物料编码" , example = "0000032")
    String storageType;//库房编码 A01

    @ApiModelProperty(value = "物料编码" , example = "[{binCode:3255644,isEmpty:1},...]")
    List<BinUseData> binUseDatas;

    @Data
    public static class BinUseData {

        @ApiModelProperty(value = "物料编码" , example = "3255644")
        String binCode;//仓位编码

        @ApiModelProperty(value = "物料编码" , example = "1")
        Integer isEmpty; //是否为空仓位 1空、0非空

    }


}
