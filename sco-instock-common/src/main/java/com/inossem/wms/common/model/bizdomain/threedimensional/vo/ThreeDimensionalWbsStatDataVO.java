package com.inossem.wms.common.model.bizdomain.threedimensional.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * WBS占比
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "WBS占比", description = "WBS占比 查询出参")
public class ThreeDimensionalWbsStatDataVO {

    @ApiModelProperty(value = "总仓位数" , example = "A01")
    String storageType;

    @ApiModelProperty(value = "总仓位数" , example = "[{wbs:\"J-1.0.0\",num:1},...]")
    List<WbsData> wbsData;

    @Data
    public static class WbsData {

        @ApiModelProperty(value = "wbs编码" , example = "J-1.0.0")
        String wbs;

        @ApiModelProperty(value = "wbs数" , example = "100")
        Integer num;
    }

}
