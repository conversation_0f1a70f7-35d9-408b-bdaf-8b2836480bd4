package com.inossem.wms.common.model.masterdata.mat.fty.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "物料工厂查询入参类", description = "物料工厂查询入参类")
@Data
public class DicMaterialFactorySearchPO extends PageCommon {

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private String ftyId;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;
    private Integer unitizedFlag;
}
