package com.inossem.wms.common.model.masterdata.spec.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 分类传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "分类传输对象", description = "分类传输对象")
public class BizSpecClassifyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/
    /**
     * 创建人描述
     */
    @ApiModelProperty(value = "创建人描述", example = "管理员")
    private String createUserName;
    /**
     * 分类类型描述
     */
    @ApiModelProperty(value = "分类类型描述" , example = "验收")
    private String specClassifyTypeI18n;
    /**
     * 特性列表
     */
    @ApiModelProperty(value = "特性列表")
    private List<BizSpecFeatureDTO> bizSpecFeatureDTOList;
    /**
     * 物料id+工厂id 作为key 的集合
     */
    @ApiModelProperty(value = "物料id+工厂id 作为key 的集合" , example = "1145343907954689")
    private Set<String> keyList;
    /**
     * 批次id
     */
    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "特性分类ID" , example = "145874414010369")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "特性分类编码" , example = "T1")
    private String specClassifyCode;

    @ApiModelProperty(value = "特性分类描述" , example = "特性描述1")
    private String specClassifyName;

    @ApiModelProperty(value = "分类类型 1验收 2物料" , example = "1")
    private Integer specClassifyType;

    @ApiModelProperty(value = "删除标识" , example = "0")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "createUserName")
    @ApiModelProperty(value = "创建人" , example = "1")
    private Long createUserId;

    @ApiModelProperty(value = "修改人" , example = "1")
    private Long modifyUserId;

}
