package com.inossem.wms.common.model.masterdata.wbs.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("dic_wbs")
@ApiModel("WBS主数据")
public class DicWbs implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("WBS编码")
    private String wbsCode;
    
    @ApiModelProperty("WBS描述")
    private String wbsName;
    
    @ApiModelProperty("地点")
    private String location;
    
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("修改时间")
    private Date modifyTime;
    
    @ApiModelProperty("创建人")
    private String createUser;
    
    @ApiModelProperty("修改人")
    private String modifyUser;
    
    @TableLogic
    private Integer isDelete;
} 