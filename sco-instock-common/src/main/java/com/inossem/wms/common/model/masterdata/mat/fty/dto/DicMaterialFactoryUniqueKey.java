package com.inossem.wms.common.model.masterdata.mat.fty.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * DicMaterialFactoryUniqueKey设计用于作为工厂物料的Hash Key使用，需要重新HashCode和Equals
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2023-05-07
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class DicMaterialFactoryUniqueKey {

    private Long matId;
    private Long ftyId;
}
