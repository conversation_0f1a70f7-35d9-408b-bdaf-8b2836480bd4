package com.inossem.wms.common.rocketmq;

import java.io.ByteArrayOutputStream;
import java.io.ObjectOutputStream;

import com.inossem.wms.common.constant.Const;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;

import lombok.extern.log4j.Log4j2;

/**
 * rocket队列-生产消息类
 *
 * <AUTHOR>
 */
@Slf4j
public class ProducerMessageContent extends Message {

    private static final long serialVersionUID = 5657381629978112241L;

    /**
     * @param topic 主题
     * @param tag 标签
     * @param body 消息体
     * <AUTHOR>
     * @date 2021/2/24 16:10
     * @return ProducerMessageContent
     */
    private ProducerMessageContent(String topic, String tag, byte[] body) {
        super(topic, tag, body);
    }

    /**
     * 生产发送消息
     * 
     * @param tag 标签
     * @param object 发送消息内容
     * @return ProducerMessageContent 消息
     */
    public static ProducerMessageContent messageContent(String tag, Object object) {
        try {
            byte[] body;
            // 序列化
            try(ByteArrayOutputStream baos = new ByteArrayOutputStream();ObjectOutputStream oos = new ObjectOutputStream(baos)) {
                oos.writeObject(object == null ? Const.STRING_EMPTY : object);
                body = baos.toByteArray();
            }

            ProducerMessageContent producerMessageContent = new ProducerMessageContent(RocketMQconfigutationProperties.TOPIC, tag, body);
            if (!tag.startsWith(RocketMQconfigutationProperties.ACKMARK)) {
                producerMessageContent.putUserProperty(RocketMQconfigutationProperties.ACKTAG, RocketMQconfigutationProperties.ACKMARK.concat(tag) + (int)(Math.random() * 10000));
                producerMessageContent.setTopic(RocketMQconfigutationProperties.TOPIC);
            }
            return producerMessageContent;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FAILURE, e.getMessage());
        }
    }

    /**
     * 直接发字节数组的消息内容，用于消息重发使用
     *
     * @param tag
     * @param body
     * @return
     * <AUTHOR> <<EMAIL>>
     */
    public static ProducerMessageContent messageContent(String tag, byte[] body) {
        try {
            ProducerMessageContent producerMessageContent = new ProducerMessageContent(RocketMQconfigutationProperties.TOPIC, tag, body);
            if (!tag.startsWith(RocketMQconfigutationProperties.ACKMARK)) {
                producerMessageContent.putUserProperty(RocketMQconfigutationProperties.ACKTAG, RocketMQconfigutationProperties.ACKMARK.concat(tag) + (int)(Math.random() * 10000));
                producerMessageContent.setTopic(RocketMQconfigutationProperties.TOPIC);
            }
            return producerMessageContent;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FAILURE, e.getMessage());
        }
    }
}
