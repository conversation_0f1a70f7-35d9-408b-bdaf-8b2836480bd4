package com.inossem.wms.common.model.masterdata.mat.info.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料净重变更记录dto
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="物料净重变更记录dto", description="物料净重变更记录dto")
public class LogMaterialNetWeightRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 开始↓ *************************/

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin", required = true)
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员", required = true)
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "重量的单位" , example = "KG")
    private String unitWeight;

    /* ********************** 扩展字段 结束↑ *************************/

    @ApiModelProperty(value = "主键id")
    private Long id;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,unitWeight", targetAttrName = "matCode,matName,unitWeight")
    @ApiModelProperty(value = "物料主键")
    private Long matId;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;
}
