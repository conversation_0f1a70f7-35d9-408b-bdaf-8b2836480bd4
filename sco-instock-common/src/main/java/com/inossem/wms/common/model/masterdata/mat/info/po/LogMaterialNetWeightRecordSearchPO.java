package com.inossem.wms.common.model.masterdata.mat.info.po;

import java.io.Serializable;
import java.util.Date;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *  物料净重变更记录PO
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LogMaterialNetWeightRecordSearchPO", description="LogMaterialNetWeightRecordSearchPO")
public class LogMaterialNetWeightRecordSearchPO extends PageCommon implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物料主键")
    private Long matId;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "创建")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;
}
