package com.inossem.wms.common.model.dictionary.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DicDictionaryVO对象", description="字典DicDictionaryVO对象")
public class DicDictionaryVO {

    private Long id;
    private Integer type;
    private String name;

}
