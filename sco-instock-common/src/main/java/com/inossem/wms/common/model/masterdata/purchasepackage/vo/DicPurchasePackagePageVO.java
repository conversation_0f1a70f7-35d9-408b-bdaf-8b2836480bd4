package com.inossem.wms.common.model.masterdata.purchasepackage.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 采购包管理
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@TableName("dic_purchase_package")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "采购包管理Page")
public class DicPurchasePackagePageVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "采购包号")
    private String purchasePackageCode;

    @ApiModelProperty(value = "采购包名称")
    private String purchasePackageName;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "采购负责人姓名")
    private String purchasePersonName;

}
