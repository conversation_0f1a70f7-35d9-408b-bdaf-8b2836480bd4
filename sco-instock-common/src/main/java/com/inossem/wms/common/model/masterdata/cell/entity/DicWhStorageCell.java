package com.inossem.wms.common.model.masterdata.cell.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 存储单元主数据
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicWhStorageCell对象", description = "存储单元主数据")
@TableName("dic_wh_storage_cell")
public class DicWhStorageCell implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "托盘编号" , example = "P001")
    private String cellCode;

    @ApiModelProperty(value = "托盘类型 0 轻型 1 重型 2 电子秤" , example = "0")
    private Integer cellType;

    @ApiModelProperty(value = "是否冻结【1是，0否】" , example = "0")
    private Integer isFreeze;

    @ApiModelProperty(value = "是否为空【1是，0否】" , example = "0")
    private Integer isEmpty;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "物料id" , example = "1", required = false)
    private Long matId;

    @ApiModelProperty(value = "仓位id" , example = "1", required = false)
    private Long binId;

    @ApiModelProperty(value = "设备状态", example = "0")
    private String electricStatus;

    @ApiModelProperty(value = "设备电量", example = "P001")
    private String electricQuantity;

    @ApiModelProperty(value = "唤醒阀值（g）", example = "P001")
    private String wakeUpThreshold;
}
