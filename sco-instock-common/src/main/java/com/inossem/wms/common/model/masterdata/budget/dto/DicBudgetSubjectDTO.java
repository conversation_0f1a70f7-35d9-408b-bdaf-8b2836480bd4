package com.inossem.wms.common.model.masterdata.budget.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "预算科目传输对象", description = "预算科目传输对象")
public class DicBudgetSubjectDTO implements Serializable {
    private static final long serialVersionUID = -712873782823997836L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "预算科目编码")
    private String budgetSubjectCode;

    @ApiModelProperty(value = "预算科目描述")
    private String budgetSubjectName;

    @RlatAttr(rlatTableName = "dic_budget_classify", sourceAttrName = "budgetClassifyCode,budgetClassifyName", targetAttrName = "budgetClassifyCode,budgetClassifyName")
    @ApiModelProperty(value = "所属预算分类id")
    private Long budgetClassifyId;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "预算分类编码")
    private String budgetClassifyCode;

    @ApiModelProperty(value = "预算分类描述")
    private String budgetClassifyName;
}
