package com.inossem.wms.common.model.masterdata.storagebin.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 仓位导出
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "仓位导出")
public class DicWhStorageBinExportVO {
    @ExcelProperty(value = "仓库")
    private String whCode;
    @ExcelProperty(value = "存储类型")
    private String typeCode;
    @ExcelProperty(value = "存储区")
    private String sectionCode;
    @ExcelProperty(value = "仓位")
    private String binCode;
}
