package com.inossem.wms.common.model.masterdata.cell.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import com.inossem.wms.common.model.masterdata.cell.dto.DicWhStorageCellDTO;
import com.inossem.wms.common.model.masterdata.cell.vo.DicWhStorageCellHttpVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 存储单元主数据查询入参对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "存储单元主数据查询入参对象", description = "存储单元主数据查询入参对象")
public class DicWhStorageCellListPO extends PageCommon {

    private static final long serialVersionUID = 1L;

    private List<DicWhStorageCellDTO> dicWhStorageCellDTOList;
}
