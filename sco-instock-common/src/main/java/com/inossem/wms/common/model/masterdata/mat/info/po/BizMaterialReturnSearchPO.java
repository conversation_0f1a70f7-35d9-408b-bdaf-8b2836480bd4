package com.inossem.wms.common.model.masterdata.mat.info.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/25 19:28
 */
@ApiModel(value = "物料保存入参类", description = "物料保存入参类")
@Data
public class BizMaterialReturnSearchPO extends PageCommon {

    @ApiModelProperty(value = "单据号" , example = "RK0001000633")
    private String receiptCode;

    @ApiModelProperty(value = "采购订单号")
    private String referReceiptCode;

    @ApiModelProperty(value = "单据类型", example = "10")
    private Integer receiptType;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "子设备物料编码")
    private String childMatCode;

    @ApiModelProperty(value = "子设备物料id")
    private Long childMatId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "创建人- 前段使用")
    private String createUserName;

    @ApiModelProperty(value = "创建人- 后端使用")
    private String userName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "状态列表" , example = "10,20")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "单据状态" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "返运类型")
    private Integer returnType;
}
