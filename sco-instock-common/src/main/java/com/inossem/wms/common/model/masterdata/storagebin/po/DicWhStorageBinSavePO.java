package com.inossem.wms.common.model.masterdata.storagebin.po;

import java.io.Serializable;

import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "仓位保存入参类", description = "仓位保存入参类")
@Data
public class DicWhStorageBinSavePO implements Serializable {

    private static final long serialVersionUID = -714832816548806020L;

    @ApiModelProperty(value = "存储类型实体类")
    private DicWhStorageBinDTO storageBinInfo;
}
