package com.inossem.wms.common.model.masterdata.mat.info.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/25 19:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "物资返运的出参", description = "物资返运的出参")
public class BizMaterialReturnHeadVO implements Serializable {

    /* =========================== 扩展字段开始 ===========================*/


    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码" , example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称" , example = "管理员")
    private String purchaseUserName;

    @SonAttr(sonTbName = "biz_material_return_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 物资返运行项目")
    private List<BizMaterialReturnItem> itemList;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称" , example = "入库单")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称" , example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "返运类型")
    private Integer returnType;

    @ApiModelProperty(value = "返运类型")
    private String returnTypeI18n;

    /* =========================== 扩展字段结束 ===========================*/


    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1.删除0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "采购负责人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "purchaseUserCode,purchaseUserName")
    private Long purchaseUserId;

}
