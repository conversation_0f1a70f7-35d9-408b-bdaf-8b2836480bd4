package com.inossem.wms.common.model.masterdata.mat.fty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 物料工厂维保方式表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DicMaterialFactoryMaintain对象", description = "物料工厂维保方式表")
@TableName("dic_material_factory_maintain")
public class DicMaterialFactoryMaintain implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "物料工厂id" , example = "145343907954689")
    private Long matFtyId;

    @ApiModelProperty(value = "是否启用" , example = "1")
    private Integer isMaintenanceEnable;

    @ApiModelProperty(value = "维保方式描述" , example = "1")
    private String maintenanceDesc;

    @ApiModelProperty(value = "维保周期" , example = "12")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}
