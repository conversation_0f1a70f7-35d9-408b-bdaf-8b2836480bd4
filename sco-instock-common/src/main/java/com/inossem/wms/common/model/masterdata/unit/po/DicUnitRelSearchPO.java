package com.inossem.wms.common.model.masterdata.unit.po;

import com.inossem.wms.common.model.common.base.PageCommon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "单位换算查询入参类", description = "单位换算查询入参类")
@Data
public class DicUnitRelSearchPO extends PageCommon {

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;
}
