package com.inossem.wms.common.model.masterdata.storagebin.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 仓位数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "仓位数据传输对象", description = "仓位数据传输对象")
public class DicWhStorageBinImport implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段 *************************/
    /** 仓库编码 */
    @ApiModelProperty(value = "仓库编码" , example = "S200")
    @ExcelProperty(value = "仓库编码", index =0)
    private String whCode;

    /** 存储类型编码 */
    @ApiModelProperty(value = "存储类型编码" , example = "801")
    @ExcelProperty(value = "存储类型编码", index =1)
    private String typeCode;

    /** 创建人编码 */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    @ExcelIgnore
    private String createUserCode;

    /** 创建人名称 */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    @ExcelIgnore
    private String createUserName;

    /** 修改人编码 */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    @ExcelIgnore
    private String modifyUserCode;

    /** 修改人名称 */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    @ExcelIgnore
    private String modifyUserName;


    /* ********************** 扩展字段 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private Long id;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    @ExcelIgnore
    private Long whId;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "typeCode,typeName")
    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    @ExcelIgnore
    private Long typeId;

    @ApiModelProperty(value = "仓位" , example = "00")
    @ExcelProperty(value = "仓位编码", index =2)
    private String binCode;


    @ApiModelProperty(value = "存储区" , example = "00")
    @ExcelProperty(value = "存储区编码", index =3)
    private String sectionCode;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    @ExcelIgnore
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    @ExcelIgnore
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    @ExcelIgnore
    private Long modifyUserId;

    @ApiModelProperty(value = "存储区", example = "", required = false)
    @ExcelIgnore
    private Long sectionId;

    @ApiModelProperty(value = "入库冻结标识【未冻结0,盘点冻结1,手动冻结2 ,wcs冻结3】" , example = "0",  required = false)
    @ExcelProperty(value = "入库冻结", index =4)
    private Integer freezeInput;

    @ApiModelProperty(value = "出库冻结标识【未冻结0,盘点冻结1,手动冻结2,wcs冻结3】" , example = "0",  required = false)
    @ExcelProperty(value = "出库冻结", index =5)
    private Integer freezeOutput;

    @ApiModelProperty(value = "备注" , example = "备注描述", required = false)
    @ExcelProperty(value = "备注", index =6)
    private String freezeReason;

    @ApiModelProperty(value = "空仓位标识【0未使用，空，1已使用，不为空】", example = "0", required = false)
    @ExcelIgnore
    private Integer isEmpty;

    @ApiModelProperty(value = "是否默认仓位【1是，0否】", example = "0", required = false)
    @ExcelIgnore
    private Integer isDefault;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @ExcelIgnore
    private Integer isDelete;

    @ApiModelProperty(value = "公司id")
    @ExcelIgnore
    private Long corpId;

    @ApiModelProperty(value = "公司编码")
    @ExcelProperty(value = "公司编码")
    private String corpCode;

    @ApiModelProperty(value = "工厂id")
    @ExcelIgnore
    private Long ftyId;

    @ApiModelProperty(value = "工厂编码")
    @ExcelProperty(value = "工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "区域架/位编号")
    @ExcelProperty(value = "区域架/位编号")
    private String groupShelfNo;

    @ApiModelProperty(value = "集团行号")
    @ExcelProperty(value = "集团行号")
    private String groupLineNo;

    @ApiModelProperty(value = "集团列号")
    @ExcelProperty(value = "集团列号")
    private String groupColumnNo;

    @ApiModelProperty(value = "集团仓位编号")
    @ExcelProperty(value = "集团仓位编号")
    private String groupBinNo;

    @ApiModelProperty(value = "是否为核岛工具间【0：否；1：是】")
    @ExcelIgnore
    private Integer isNuclearIslandToolRoom;

    @ExcelProperty(value = "承重（kg）")
    @ApiModelProperty(value = "仓位承重（KG）")
    private BigDecimal weight;

    @ExcelIgnore
    @ApiModelProperty(value = "是否超重")
    private String overWeight;
}
