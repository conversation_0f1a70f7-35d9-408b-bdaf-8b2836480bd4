package com.inossem.wms.common.model.masterdata.cell.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 存储单元类推送设备状态、电量VO
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-012-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "存储单元类推送设备状态、电量VO", description = "存储单元类推送设备状态、电量VO")
public class DicWhStorageCellPushVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "推送失败的电子ID")
    private String failureElectronicScaleId;

    @ApiModelProperty(value = "推送失败信息")
    private String failureMsg;
}
