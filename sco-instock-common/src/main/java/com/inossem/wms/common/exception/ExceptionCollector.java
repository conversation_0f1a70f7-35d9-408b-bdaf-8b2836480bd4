package com.inossem.wms.common.exception;

import java.util.LinkedList;
import java.util.List;

import com.inossem.wms.common.enums.EnumReturnMessageLevel;

/**
 * 说明: 异常收集器，主要用在接口正常返回数据时，需要额外携带异常提示时使用。
 * <p>
 * 通过ThreadLocal的单例多线程特性，将异常信息按照不同线程进行收集<br>
 * 每个线程在调用<code>add()</code>方法时，只收集本线程自己的异常信息<br>
 * 收集后的异常信息将在com.inossem.wms.exception.ExceptionAdditionalHandler补充异常处理器中进行处理
 * </p>
 * 
 * <AUTHOR> 创建时间: 2020年2月26日
 */
public class ExceptionCollector {

    private static final ThreadLocal<List<WmsException>> exceptionList = ThreadLocal.withInitial(LinkedList::new);

    private static final ThreadLocal<EnumReturnMessageLevel> exceptionLevel = ThreadLocal.withInitial(() -> {
        // 默认设置为警告级别
        return EnumReturnMessageLevel.WARN;
    });

    public static void add(WmsException e) {
        List<WmsException> eList = exceptionList.get();
        eList.add(e);
        exceptionList.set(eList);
    }

    public static List<WmsException> getAll() {
        return exceptionList.get();
    }

    public static void clear() {
        exceptionList.remove();
        exceptionLevel.remove();
    }

    public static EnumReturnMessageLevel getLevel() {
        return exceptionLevel.get();
    }

    /**
     * 设置异常收集器中所有异常信息在提示时的消息返回级别<br>
     * 用于处理前端弹窗的样式
     * 
     * @param level 级别
     * <AUTHOR>
     */
    public static void setLevel(EnumReturnMessageLevel level) {
        exceptionLevel.set(level);
    }

}
