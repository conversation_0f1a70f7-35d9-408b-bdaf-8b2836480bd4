package com.inossem.wms.common.model.masterdata.asset.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("dic_asset")
@ApiModel("资产卡片主数据")
public class DicAsset implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("资产编码")
    private String assetCode;
    
    @ApiModelProperty("资产子编号") 
    private String assetSubCode;
    
    @ApiModelProperty("资产类型")
    private String assetType;
    
    @ApiModelProperty("资产描述")
    private String assetName;
    
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("修改时间")
    private Date modifyTime;
    
    @ApiModelProperty("创建人")
    private String createUser;
    
    @ApiModelProperty("修改人")
    private String modifyUser;
    
    @TableLogic
    private Integer isDelete;
} 