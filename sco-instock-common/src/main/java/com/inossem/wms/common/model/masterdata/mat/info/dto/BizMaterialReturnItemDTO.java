package com.inossem.wms.common.model.masterdata.mat.info.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/29 10:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "物资返运数据对象传输", description = "物资返运数据对象传输")
public class BizMaterialReturnItemDTO  implements Serializable {


    private static final long serialVersionUID = 1L;

    /* =========================== 扩展字段开始 ===========================*/


    @ApiModelProperty(value = "填充功能属性-订单数量" , example = "10")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "单位code" , example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称" , example = "物料描述001003")
    private String matName;

    /* =========================== 扩展字段结束 ===========================*/



    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "入库单行项目序号")
    private String referReceiptRid;

    @ApiModelProperty(value = "采购订单号")
    private String referReceiptCode;

    @ApiModelProperty(value = "采购订单head表id")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "采购订单item表id")
    @RlatAttr(rlatTableName = "erp_purchase_receipt_item", sourceAttrName = "contractCode,receiptQty,subjectType", targetAttrName = "contractCode,preReceiptQty,subjectType")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "物料id")
    @RlatAttr(rlatTableName = "dic_material",sourceAttrName = "matCode,matName",targetAttrName = "matCode,matName")
    private Long matId;

    @ApiModelProperty(value = "单位id")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    private Long unitId;


    @ApiModelProperty(value = "填充功能属性-合同号" , example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "返运数量" , example = "10")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "包装形式" , example = "10")
    private String packageStyle;

    @ApiModelProperty(value = "返运原因" , example = "10")
    private String returnReason;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "扩展属性 - 批次图片")
    @SonAttr(sonTbName = "biz_batch_img", sonTbFkAttrName = "receiptItemId")
    private List<BizBatchImgDTO> bizBatchImgDTOList;

    @SonAttr(sonTbName = "biz_material_return_waybill", sonTbFkAttrName = "itemId")
    @ApiModelProperty(value = "扩展属性 - 运单")
    private List<BizMaterialReturnWaybillDTO> waybillDTOList;


    @ApiModelProperty(value = "填充属性 - 子物料id")
    private Long childMatId;
    @ApiModelProperty(value = "填充属性 - 子物料编码")
    private String childMatCode;
    @ApiModelProperty(value = "填充属性 - 子物料名称")
    private String childMatName;

    @ApiModelProperty(value = "前续单据head主键")
    @RlatAttr(rlatTableName = "biz_receipt_inconformity_head", sourceAttrName = "differentType", targetAttrName = "differentType")
    private Long preReceiptHeadId;

    @RlatAttr(rlatTableName = {Const.PRE_RECEIPT_TYPE_INPUT, Const.PRE_RECEIPT_TYPE_INCONFORMITY},
            sourceAttrName = "batchId,qty,matDocCode,writeOffMatDocCode,deliveryWriteOffMatDocCode",
            targetAttrName = "batchId,inputQty,matDocCode,writeOffMatDocCode,deliveryWriteOffMatDocCode")
    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "填充属性 - 前续单据号")
    private String preReceiptCode;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "多供的物料编码")
    private String extraMatCode;

    @ApiModelProperty(value = "多供的物料描述")
    private String extraMatName;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "extraUnitCode,extraUnitName")
    @ApiModelProperty(value = "多供物料的单位id")
    private Long extraUnitId;

    @ApiModelProperty(value = "多供物料填写的需求人")
    private String extraApplyUser;

    @ApiModelProperty(value = "多供物料的单位code")
    private String extraUnitCode;

    @ApiModelProperty(value = "多供物料的单位name")
    private String extraUnitName;

    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "batchCode", targetAttrName = "batchCode")
    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @ApiModelProperty(value = "批次编码")
    private String batchCode;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "物料凭证")
    private String matDocCode;

    @ApiModelProperty(value = "106凭证")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "104凭证")
    private String deliveryWriteOffMatDocCode;

    @ApiModelProperty(value = "已冲销数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "差异类型")
    private Integer differentType;

    @ApiModelProperty(value = "科目类型")
    private String subjectType;

    /**
     * 仓位库存
     */
    @ApiModelProperty(value = "仓位库存")
    private List<StockBinDTO> stockBinList;


}
