package com.inossem.wms.common.model.masterdata.mat.info.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 物料分页对象出参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "物料分页对象出参", description = "物料分页对象出参")
public class DicMaterialPageExportVO {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ExcelProperty(value = "物料描述")
    private String matName;

    @ExcelProperty(value = "物料描述（英文）")
    private String matNameEn;

    @ExcelProperty(value = "物料类型")
    private String matType;
    @ExcelIgnore
    private String matTypeCode;
    @ExcelIgnore
    private String matTypeName;

    @ExcelProperty(value = "物料组")
    private String matGroup;
    @ExcelIgnore
    private String matGroupCode;
    @ExcelIgnore
    private String matGroupName;

    @ExcelProperty(value = "计量单位")
    private String unit;
    @ExcelIgnore
    private String unitCode;
    @ExcelIgnore
    private String unitName;
}
